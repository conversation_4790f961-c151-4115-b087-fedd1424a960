# Grid२Play Booking & Payment Policy Documentation

## 🎯 **OVERVIEW**

Grid२Play operates a comprehensive sports booking platform with a strict online-only payment policy designed to ensure transparency, real-time data accuracy, and seamless revenue management. This documentation outlines all booking procedures, payment policies, and platform features.

---

## 💰 **DISCOUNT SYSTEM**

### **Coupon Discovery & Application**
- **Location**: Users can view available discount coupons on the "Venue Details" page
- **Application Process**: 
  1. Copy the coupon code from the venue details page
  2. Navigate to Step 3 of the booking process on BookingPage
  3. Enter the coupon code in the designated field
  4. System validates the coupon in real-time

### **Coupon Validation System**
- **Security**: Enhanced validation with rate limiting and CSRF protection
- **Validation Checks**:
  - Coupon code authenticity and format
  - Venue-specific applicability
  - Expiry date verification
  - Usage limit enforcement (max_uses vs current_uses)
  - User eligibility verification
- **Real-time Feedback**: Immediate validation results with success/error messages

### **Discount Calculation**
- **Types**: Percentage-based or fixed amount discounts
- **Application**: Discounts applied to original booking amount
- **Platform Fee**: Calculated on original amount (before discount)
- **Final Amount**: User pays discounted amount, venue receives settlement based on original amount minus platform fees

---

## 💳 **PAYMENT POLICY - ONLINE ONLY**

### **Strategic Rationale**
Grid२Play maintains a strict online-only payment policy for the following business-critical reasons:

#### **1. Real-time Data Accuracy**
- **Immediate Availability**: All booking data is instantly available to venue admins
- **Live Updates**: Real-time slot availability prevents double bookings
- **Instant Confirmation**: Automated booking confirmations via WhatsApp/SMS

#### **2. Transparent Revenue Tracking**
- **Dynamic Calculations**: All revenue calculations use database-driven values
- **Platform Fee**: Venue-specific rates (default 5%) from `venues.platform_fee_percentage`
- **TDS Implementation**: Automated TDS calculation (default 1%) on platform fees
- **Real-time Reports**: Daily WhatsApp revenue reports with accurate financial data

#### **3. Automated Settlement Processing**
- **Weekly Settlements**: Automated revenue distribution to venue partners
- **Transparent Calculations**: Clear breakdown of gross revenue, platform fees, and TDS
- **Audit Trail**: Complete transaction history for financial compliance

#### **4. Fraud Prevention & Security**
- **Payment Gateway**: Secure Razorpay integration with encrypted transactions
- **Audit Compliance**: Complete transaction records for regulatory requirements
- **Dispute Resolution**: Automated refund processing through payment gateway

---

## 🚫 **NO PARTIAL PAYMENTS OR OFFLINE OPTIONS**

### **Payment Requirements**
- **Full Payment**: Complete payment required at booking confirmation
- **No Cash Payments**: Regular users cannot make cash payments
- **Payment Gateway**: All transactions processed through Razorpay
- **Payment Timeout**: 15-minute payment window for order completion

### **Security Measures**
- **Server-side Validation**: Price validation on backend to prevent tampering
- **CSRF Protection**: Token-based security for all payment requests
- **Rate Limiting**: Protection against payment abuse and fraud attempts
- **Secure Order Creation**: Enhanced Edge Functions with comprehensive validation

---

## 👨‍💼 **ADMIN OVERRIDE - "BOOK FOR CUSTOMER" FEATURE**

### **Purpose & Functionality**
- **Flexibility**: Venue admins can create offline bookings for special circumstances
- **System Integrity**: Maintains booking system consistency while providing admin flexibility
- **Separate Tracking**: Admin bookings stored in dedicated `admin_bookings` table

### **Admin Booking Process**
1. **Access**: Available through admin dashboard (mobile & desktop)
2. **Court Selection**: Choose available courts and time slots
3. **Customer Details**: Enter customer name and phone number
4. **Payment Method**: Select 'cash' or 'online' payment method
5. **Amount Collection**: Record actual amount collected from customer
6. **Booking Creation**: System creates booking with admin attribution

### **Data Separation**
- **Regular Bookings**: Stored in `bookings` table with online payment tracking
- **Admin Bookings**: Stored in `admin_bookings` table with admin attribution
- **Revenue Impact**: Admin cash bookings excluded from online settlement calculations
- **Reporting**: Separate tracking in daily revenue reports (informational vs settlement)

---

## 🔄 **REFUND SYSTEM**

### **Refund Policy**
- **Automated Processing**: Refunds processed through Razorpay payment gateway
- **Refund Timeline**: 5-7 business days for bank credit
- **Full Refunds**: Complete amount refunded for eligible cancellations
- **Admin Override**: Venue admins can process custom refund amounts

### **Refund Process**
1. **Cancellation Request**: User or admin initiates booking cancellation
2. **Refund Calculation**: System calculates eligible refund amount
3. **Gateway Processing**: Refund initiated through Razorpay
4. **Status Tracking**: Real-time refund status updates
5. **Confirmation**: Automated notifications to user and venue admin

### **Refund Tracking**
- **Database Storage**: Complete refund history in `cancellations` table
- **Status Management**: Pending → Processed → Completed status flow
- **Admin Dashboard**: Comprehensive refund management interface
- **Audit Trail**: Complete refund transaction history

---

## ❌ **CANCELLATION POLICY**

### **Cancellation Windows**
- **User Cancellations**: Allowed up to 2 hours before booking start time
- **Admin Cancellations**: Venue admins can cancel bookings with appropriate reason
- **Emergency Cancellations**: Special provisions for weather or facility issues

### **Cancellation Process**
1. **Initiation**: User or admin initiates cancellation request
2. **Reason Selection**: Choose from predefined cancellation reasons
3. **Refund Eligibility**: System determines refund amount based on timing
4. **Processing**: Automated cancellation processing with notifications
5. **Confirmation**: WhatsApp/SMS confirmation to all parties

### **Cancellation Tracking**
- **Role Attribution**: Track whether cancelled by user or admin
- **Reason Logging**: Detailed cancellation reason tracking
- **Financial Impact**: Separate tracking of cancelled booking revenue
- **Reporting**: Cancelled bookings shown separately in all reports

---

## 💼 **REVENUE SYSTEM**

### **Transparent Revenue Sharing Model**
Grid२Play operates on a transparent revenue sharing model with the following distribution:

#### **Standard Bookings**
- **Venue Partner**: 95% of booking amount (after platform fee deduction)
- **Platform Fee**: 5% (configurable per venue in database)
- **TDS Deduction**: 1% of platform fee (for tax compliance)

#### **Tournament Revenue** (Future Implementation)
- **Venue Partner**: 60-70% of entry fees
- **Prize Pool**: 20-25% of entry fees  
- **Grid२Play Platform**: 10-15% of entry fees
- **Payment Processing**: 2-3% of entry fees

### **Revenue Calculation Example**
```
Booking Amount: ₹1,200
Platform Fee (5%): ₹60.00
TDS (1% of platform fee): ₹0.60
Net Settlement to Venue: ₹1,139.40
```

---

## 🏦 **SETTLEMENT PROCESS**

### **Automated Settlement System**
- **Frequency**: Weekly automated settlements every Monday
- **Calculation**: Database-driven calculations using venue-specific rates
- **Validation**: Multi-level verification before settlement processing
- **Transparency**: Complete settlement breakdown provided to venues

### **Settlement Components**
- **Online Revenue**: Only online bookings included in settlements
- **Platform Fee Deduction**: Venue-specific platform fee percentage
- **TDS Compliance**: Automated TDS calculation and deduction
- **Net Settlement**: Final amount transferred to venue account

### **Settlement Tracking**
- **Status Management**: Pending → Processed → Settled status flow
- **Admin Dashboard**: Comprehensive settlement management interface
- **Audit Trail**: Complete settlement history and documentation
- **Bank Integration**: Automated bank transfer initiation

---

## 📊 **BOOKING MANAGEMENT FEATURES**

### **Real-time Availability**
- **Live Updates**: Real-time slot availability across all platforms
- **Court Groups**: Shared space logic for multi-court venues
- **Capacity Management**: Support for capacity-based and court-based bookings
- **Conflict Prevention**: Automatic prevention of double bookings

### **Booking Modifications**
- **Rescheduling**: Limited rescheduling options based on availability
- **Upgrades**: Court upgrade options when available
- **Guest Management**: Add/modify guest information for bookings
- **Special Requests**: Notes and special requirements handling

### **Customer Support Features**
- **Booking History**: Complete booking history for users and admins
- **Issue Resolution**: Integrated support ticket system
- **Communication**: WhatsApp/SMS integration for all communications
- **Escalation**: Multi-level support escalation process

---

## 📱 **MOBILE-FIRST EXPERIENCE**

### **User Interface**
- **Touch Optimization**: 48px minimum touch targets for mobile
- **Responsive Design**: Optimized for 90% mobile user base
- **Progressive Web App**: Native app-like experience
- **Dark Theme**: Emerald green (#10b981) and black (#0f172a) color scheme

### **Performance Optimization**
- **Lazy Loading**: Optimized image and content loading
- **Offline Support**: Basic offline functionality for bookings
- **Fast Loading**: Optimized for mobile network conditions
- **Battery Efficiency**: Reduced animation and processing for mobile

---

## 🔒 **SECURITY & COMPLIANCE**

### **Data Protection**
- **GDPR Compliance**: User consent tracking and data protection
- **PCI DSS**: Secure payment processing standards
- **Encryption**: End-to-end encryption for sensitive data
- **Audit Logging**: Comprehensive security event logging

### **Legal Compliance**
- **Age Verification**: Automated age verification for users
- **Terms Acceptance**: Mandatory legal agreement acceptance
- **Data Processing**: Transparent data processing activity logging
- **Consent Management**: Granular consent tracking and management

---

*This documentation reflects the current Grid२Play platform implementation as of January 2025. All policies and procedures are subject to updates based on business requirements and regulatory changes.*
