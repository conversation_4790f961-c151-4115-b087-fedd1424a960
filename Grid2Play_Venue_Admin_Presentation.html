<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid२Play - Venue Admin Partner Presentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #ffffff;
            line-height: 1.6;
        }
        
        .slide {
            min-height: 100vh;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            page-break-after: always;
            position: relative;
        }
        
        .slide-header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .slide-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #10b981, #059669);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }
        
        .slide-subtitle {
            font-size: 1.5rem;
            color: #94a3b8;
            font-weight: 300;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .feature-card {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #10b981;
            margin-bottom: 15px;
        }
        
        .feature-description {
            color: #cbd5e1;
            font-size: 1rem;
            line-height: 1.7;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .stat-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 12px;
            padding: 25px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #10b981;
            display: block;
        }
        
        .stat-label {
            color: #94a3b8;
            font-size: 0.9rem;
            margin-top: 8px;
        }
        
        .logo {
            position: absolute;
            top: 30px;
            left: 40px;
            font-size: 1.8rem;
            font-weight: 700;
            color: #10b981;
        }
        
        .slide-number {
            position: absolute;
            bottom: 30px;
            right: 40px;
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
            border-left: 4px solid #10b981;
            padding: 25px;
            margin: 30px 0;
            border-radius: 8px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .data-table th {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            font-weight: 600;
        }
        
        .data-table td {
            color: #cbd5e1;
        }
        
        .cta-section {
            text-align: center;
            margin-top: 50px;
            padding: 40px;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 16px;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 15px 40px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .mobile-mockup {
            max-width: 300px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 25px;
            padding: 20px;
            border: 2px solid #10b981;
        }
        
        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        
        .process-step {
            flex: 1;
            text-align: center;
            margin: 10px;
            min-width: 200px;
        }
        
        .process-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 auto 15px;
        }
        
        .arrow {
            color: #10b981;
            font-size: 2rem;
            margin: 0 20px;
        }
        
        @media (max-width: 768px) {
            .slide {
                padding: 40px 20px;
            }
            
            .slide-title {
                font-size: 2rem;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .process-flow {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
                margin: 20px 0;
            }
        }
        
        /* Interactive Elements & Tooltips */
        .admin-tooltip {
            position: relative;
            cursor: help;
            color: #10b981;
            font-weight: 600;
            border-bottom: 1px dotted #10b981;
        }

        .admin-tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 1000;
            border: 1px solid #10b981;
        }

        .admin-tooltip:hover::before {
            content: '';
            position: absolute;
            bottom: 92%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        /* Progress Indicators */
        .progress-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(16, 185, 129, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #94a3b8;
        }

        .progress-step.active {
            color: #10b981;
            font-weight: 600;
        }

        /* Step Cards for Booking Walkthrough */
        .step-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .step-card {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 16px;
            padding: 30px;
            position: relative;
            transition: all 0.3s ease;
        }

        .step-card:hover {
            transform: translateY(-5px);
            border-color: rgba(16, 185, 129, 0.5);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 30px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            border: 3px solid #0f172a;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #10b981;
            margin: 20px 0 15px;
        }

        .step-description {
            color: #cbd5e1;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .step-cta {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .step-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        /* Media Placeholders */
        .media-placeholder {
            background: rgba(0, 0, 0, 0.4);
            border: 2px dashed rgba(16, 185, 129, 0.5);
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            margin: 20px 0;
            color: #94a3b8;
            font-style: italic;
            position: relative;
        }

        .media-placeholder::before {
            content: '📷';
            font-size: 2rem;
            display: block;
            margin-bottom: 10px;
        }

        .media-placeholder[data-media-type="video"]::before {
            content: '🎥';
        }

        .media-caption {
            display: block;
            font-size: 0.8rem;
            color: #64748b;
            margin-top: 10px;
            font-style: normal;
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            background: rgba(16, 185, 129, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
            animation: slideInRight 0.3s ease;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Collapsible Panels */
        .collapsible-panel {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 12px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .panel-header {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(16, 185, 129, 0.1);
            transition: background 0.3s ease;
        }

        .panel-header:hover {
            background: rgba(16, 185, 129, 0.2);
        }

        .panel-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #10b981;
        }

        .panel-toggle {
            font-size: 1.2rem;
            color: #10b981;
            transition: transform 0.3s ease;
        }

        .panel-content {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .panel-content.expanded {
            padding: 20px;
            max-height: 500px;
        }

        /* Enhanced Touch Targets for Mobile */
        @media (max-width: 768px) {
            .step-cta,
            .cta-button {
                padding: 15px 25px;
                font-size: 1rem;
                min-height: 48px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .feature-card {
                padding: 25px;
                margin-bottom: 20px;
            }

            .step-cards-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .admin-tooltip:hover::after {
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                transform: none;
                white-space: normal;
                text-align: center;
            }

            .admin-tooltip:hover::before {
                display: none;
            }
        }

        @media print {
            .slide {
                page-break-after: always;
            }

            .admin-tooltip::after,
            .toast-container,
            .step-cta {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Slide 1: Title Slide -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">Grid२Play</h1>
            <p class="slide-subtitle">Venue Admin Partner Platform</p>
            <div style="margin-top: 40px;">
                <h2 style="color: #10b981; font-size: 2rem; margin-bottom: 20px;">Complete Venue Management Solution</h2>
                <p style="font-size: 1.2rem; color: #cbd5e1; max-width: 800px; margin: 0 auto;">
                    Empowering sports venue partners with transparent revenue management, 
                    real-time booking control, and comprehensive analytics
                </p>
            </div>
        </div>
        
        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">90%</span>
                <div class="stat-label">Mobile Users</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">100%</span>
                <div class="stat-label">Financial Transparency</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">24/7</span>
                <div class="stat-label">Real-time Updates</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">5%</span>
                <div class="stat-label">Platform Fee</div>
            </div>
        </div>
        
        <div class="slide-number">1</div>
    </div>

    <!-- Slide 2: Financial Transparency -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">💰 Financial Transparency</h1>
            <p class="slide-subtitle">Our Primary USP - Complete Revenue Visibility</p>
        </div>
        
        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">🎯 Zero Hidden Charges Policy</h3>
            <p>Every ₹1 is accounted for with complete breakdown of platform fees, TDS calculations, and settlement amounts. No free slots given when admin cancels - complete transparency in all transactions.</p>
        </div>
        
        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">📊</span>
                <h3 class="feature-title">Revenue Breakdown</h3>
                <p class="feature-description">
                    <span class="admin-tooltip" data-tooltip="Dynamic fee calculated from venues database table">Platform fee</span>
                    calculations are database-driven (not hardcoded). Dynamic TDS rates per venue.
                    Instant revenue updates with every booking confirmation.
                </p>
                <div class="media-placeholder" data-media-type="video" data-caption="Live Revenue Calculation Demo">
                    Video: Live demonstration of revenue calculation showing database-driven platform fee and TDS calculations
                </div>
                <span class="media-caption">Live Revenue Calculation Demo</span>
            </div>

            <div class="feature-card">
                <span class="feature-icon">💳</span>
                <h3 class="feature-title">Platform Fee Calculator</h3>
                <p class="feature-description">
                    Interactive calculator showing exact fee breakdown for any booking amount.
                    Transparent calculations with no hidden charges or surprise deductions.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">⏰</span>
                <h3 class="feature-title">Settlement Timeline</h3>
                <p class="feature-description">
                    Clear settlement schedule with automated processing. Weekly settlements
                    with detailed reports and instant notifications for all transactions.
                </p>
            </div>
        </div>
        
        <h3 style="color: #10b981; margin: 40px 0 20px;">Sample Revenue Calculation</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Booking Reference</th>
                    <th>Gross Amount</th>
                    <th>Platform Fee (5%)</th>
                    <th>TDS (1%)</th>
                    <th>Net Settlement</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>GR2P-59727807</td>
                    <td>₹1,200</td>
                    <td>₹60</td>
                    <td>₹0.60</td>
                    <td>₹1,139.40</td>
                </tr>
                <tr>
                    <td>GR2P-14215B94</td>
                    <td>₹650</td>
                    <td>₹32.50</td>
                    <td>₹0.33</td>
                    <td>₹617.17</td>
                </tr>
            </tbody>
        </table>
        
        <div class="slide-number">2</div>
    </div>

    <!-- Slide 3: Booking Management -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">📅 Seamless Booking Management</h1>
            <p class="slide-subtitle">Complete Control Over Your Venue Operations</p>
        </div>

        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3 class="feature-title">Real-time Booking Updates</h3>
                <p class="feature-description">
                    Instant notifications for new bookings, cancellations, and modifications.
                    Live dashboard updates with booking status changes.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🏟️</span>
                <h3 class="feature-title">Court Availability Management</h3>
                <p class="feature-description">
                    Smart shared space logic for multi-sport courts.
                    Real-time availability tracking with slot blocking capabilities.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">👥</span>
                <h3 class="feature-title">Admin Booking for Walk-ins</h3>
                <p class="feature-description">
                    Book slots directly for walk-in customers.
                    Flexible payment options (cash/online) with proper tracking.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">✅</span>
                <h3 class="feature-title">Booking Workflow Control</h3>
                <p class="feature-description">
                    Confirm, cancel, or modify bookings with proper audit trails.
                    Automated refund processing and customer notifications.
                </p>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">📱 Mobile-First Design</h3>
            <p>90% of our users are on mobile. The admin interface is optimized for mobile management with 48px touch targets and intuitive navigation.</p>
        </div>

        <div class="slide-number">3</div>
    </div>

    <!-- Slide 4.5: Interactive Booking Management Walkthrough -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">📋 Manage Bookings in 3 Steps</h1>
            <p class="slide-subtitle">Interactive Booking Management Walkthrough</p>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 100%;"></div>
            </div>
            <div class="progress-steps">
                <span class="progress-step active">Request</span>
                <span class="progress-step active">Review</span>
                <span class="progress-step active">Approve</span>
                <span class="progress-step active">Notify</span>
            </div>
        </div>

        <div class="step-cards-container">
            <div class="step-card">
                <div class="step-number">1</div>
                <h3 class="step-title">View Real-time Requests</h3>
                <p class="step-description">
                    Receive instant notifications for new booking requests through your
                    <span class="admin-tooltip" data-tooltip="Live updates every 30 seconds">real-time dashboard</span>.
                    See customer details, preferred time slots, and payment status immediately.
                </p>
                <div class="media-placeholder" data-media-type="screenshot">
                    Screenshot: Admin dashboard showing incoming booking notifications with customer details and time slot preferences
                </div>
                <span class="media-caption">Live booking request notification in admin dashboard</span>
                <button class="step-cta">Try This Step</button>
            </div>

            <div class="step-card">
                <div class="step-number">2</div>
                <h3 class="step-title">Approve & Modify</h3>
                <p class="step-description">
                    Review booking details and make necessary modifications. Adjust time slots,
                    court assignments, or pricing with our flexible booking management system.
                    All changes are tracked with complete audit trails.
                </p>
                <div class="media-placeholder" data-media-type="screenshot">
                    Screenshot: Booking modification interface showing time slot adjustment, court selection, and pricing controls
                </div>
                <span class="media-caption">Booking approval and modification interface</span>
                <button class="step-cta">See Demo</button>
            </div>

            <div class="step-card">
                <div class="step-number">3</div>
                <h3 class="step-title">Confirm & Notify</h3>
                <p class="step-description">
                    Once approved, customers receive automatic
                    <span class="admin-tooltip" data-tooltip="Automated daily revenue reports at 12 AM">WhatsApp notifications</span>
                    with booking confirmations, court details, and payment receipts. You get instant revenue updates.
                </p>
                <div class="media-placeholder" data-media-type="screenshot">
                    Screenshot: WhatsApp notification being sent to customer with booking confirmation and venue admin receiving revenue update
                </div>
                <span class="media-caption">Automated customer notification via WhatsApp</span>
                <button class="step-cta">Try This Step</button>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">⚡ Average Processing Time: 30 seconds</h3>
            <p>From booking request to customer confirmation, our streamlined 3-step process ensures quick turnaround times while maintaining complete transparency and control.</p>
        </div>

        <div class="slide-number">4</div>
    </div>

    <!-- Slide 4: Communication & Notifications -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">📱 Communication & Notifications</h1>
            <p class="slide-subtitle">Stay Connected with Your Business</p>
        </div>

        <div class="process-flow">
            <div class="process-step">
                <div class="process-number">1</div>
                <h4 style="color: #10b981;">Booking Made</h4>
                <p style="color: #cbd5e1;">Customer books a slot</p>
            </div>
            <div class="arrow">→</div>
            <div class="process-step">
                <div class="process-number">2</div>
                <h4 style="color: #10b981;">Instant WhatsApp</h4>
                <p style="color: #cbd5e1;">Admin gets notification</p>
            </div>
            <div class="arrow">→</div>
            <div class="process-step">
                <div class="process-number">3</div>
                <h4 style="color: #10b981;">Daily Report</h4>
                <p style="color: #cbd5e1;">12 AM revenue summary</p>
            </div>
        </div>

        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">💬</span>
                <h3 class="feature-title">WhatsApp Integration</h3>
                <p class="feature-description">
                    • Instant booking confirmations<br>
                    • Cancellation notifications<br>
                    • <span class="admin-tooltip" data-tooltip="Automated daily revenue reports at 12 AM">WhatsApp Reports</span> at 12 AM<br>
                    • Live booking status updates<br>
                    • Weekly earnings summaries
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🔔</span>
                <h3 class="feature-title">Real-time Dashboard</h3>
                <p class="feature-description">
                    • <span class="admin-tooltip" data-tooltip="Live updates every 30 seconds">Real-time Dashboard</span> notifications<br>
                    • Revenue updates<br>
                    • Court availability changes<br>
                    • Customer feedback alerts<br>
                    • System status updates
                </p>
            </div>
        </div>

        <div class="media-placeholder" data-media-type="screenshot" data-caption="Actual WhatsApp Daily Report">
            Screenshot: Actual WhatsApp daily revenue report showing venue name, booking summary, revenue breakdown, and settlement details
        </div>
        <span class="media-caption">Actual WhatsApp daily revenue report sent to venue admins</span>

        <div class="mobile-mockup">
            <h4 style="color: #10b981; text-align: center; margin-bottom: 15px;">Sample WhatsApp Report</h4>
            <div style="background: #0f172a; padding: 15px; border-radius: 8px; font-size: 0.9rem;">
                <p style="color: #10b981; font-weight: bold;">Grid२Play Daily Report</p>
                <p style="color: #cbd5e1;">📅 22/07/2025 - East Delhi Box</p>
                <p style="color: #cbd5e1;">📊 Total Bookings: 4</p>
                <p style="color: #cbd5e1;">💰 Gross Revenue: ₹3,250</p>
                <p style="color: #cbd5e1;">💳 Platform Fee: ₹163 (5%)</p>
                <p style="color: #cbd5e1;">🏦 Net Settlement: ₹3,086</p>
            </div>
        </div>

        <!-- Toast Notification Demo -->
        <div class="toast-container" style="position: relative; top: 0; right: 0; margin-top: 20px;">
            <div class="toast">Booking Approved! ✅</div>
            <div class="toast" style="animation-delay: 0.5s;">Revenue Report Generated 📊</div>
            <div class="toast" style="animation-delay: 1s;">Customer Notified via WhatsApp 💬</div>
        </div>

        <div class="slide-number">5</div>
    </div>

    <!-- Slide 6: Comprehensive Reporting -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">📈 Comprehensive Reporting & Analytics</h1>
            <p class="slide-subtitle">Data-Driven Business Insights</p>
        </div>

        <div class="collapsible-panels-container">
            <div class="collapsible-panel">
                <div class="panel-header" onclick="togglePanel(this)">
                    <div class="panel-title">📊 Daily Reports</div>
                    <div class="panel-toggle">+</div>
                </div>
                <div class="panel-content">
                    <p style="color: #cbd5e1; margin-bottom: 15px;">
                        • Real-time revenue tracking with instant updates<br>
                        • Online vs offline revenue separation<br>
                        • Booking count analytics and trends<br>
                        • Performance comparisons with previous periods
                    </p>
                    <div class="media-placeholder" data-media-type="screenshot">
                        Screenshot: Daily earnings dashboard showing revenue breakdown, booking counts, and trend analysis
                    </div>
                    <span class="media-caption">Daily earnings dashboard interface</span>
                </div>
            </div>

            <div class="collapsible-panel">
                <div class="panel-header" onclick="togglePanel(this)">
                    <div class="panel-title">💰 Weekly Settlements</div>
                    <div class="panel-toggle">+</div>
                </div>
                <div class="panel-content">
                    <p style="color: #cbd5e1; margin-bottom: 15px;">
                        • Automated weekly settlement processing<br>
                        • <span class="admin-tooltip" data-tooltip="Dynamic fee calculated from venues database table">Platform fee</span> calculations with transparency<br>
                        • TDS deduction tracking and reporting<br>
                        • Payment status monitoring and alerts
                    </p>
                </div>
            </div>

            <div class="collapsible-panel">
                <div class="panel-header" onclick="togglePanel(this)">
                    <div class="panel-title">📋 Excel Exports</div>
                    <div class="panel-toggle">+</div>
                </div>
                <div class="panel-content">
                    <p style="color: #cbd5e1; margin-bottom: 15px;">
                        • <span class="admin-tooltip" data-tooltip="Download detailed reports with booking references">Excel Exports</span> with booking reference IDs<br>
                        • Custom date range analysis and filtering<br>
                        • Revenue breakdown by payment method<br>
                        • Court utilization statistics and insights
                    </p>
                    <div class="media-placeholder" data-media-type="screenshot">
                        Screenshot: Excel export interface showing date range selection, filter options, and sample exported spreadsheet
                    </div>
                    <span class="media-caption">Excel export functionality with custom date ranges</span>

                    <div class="media-placeholder" data-media-type="screenshot" style="margin-top: 15px;">
                        Screenshot: Sample Excel report showing booking details, revenue calculations, and reference IDs
                    </div>
                    <span class="media-caption">Sample Excel report with detailed booking information</span>
                </div>
            </div>

            <div class="collapsible-panel">
                <div class="panel-header" onclick="togglePanel(this)">
                    <div class="panel-title">🎯 Analytics Dashboard</div>
                    <div class="panel-toggle">+</div>
                </div>
                <div class="panel-content">
                    <p style="color: #cbd5e1; margin-bottom: 15px;">
                        • Peak hour analysis and optimization suggestions<br>
                        • Customer behavior insights and patterns<br>
                        • Occupancy rate tracking and forecasting<br>
                        • Growth trend monitoring and projections
                    </p>
                </div>
            </div>
        </div>

        <h3 style="color: #10b981; margin: 40px 0 20px;">Sample Daily Earnings Data</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Total Bookings</th>
                    <th>Online Revenue</th>
                    <th>Platform Fee</th>
                    <th>Net Settlement</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>22/07/2025</td>
                    <td>4</td>
                    <td>₹3,250</td>
                    <td>₹162.50</td>
                    <td>₹3,085.87</td>
                </tr>
                <tr>
                    <td>23/07/2025</td>
                    <td>1</td>
                    <td>₹1,200</td>
                    <td>₹60.00</td>
                    <td>₹1,139.40</td>
                </tr>
            </tbody>
        </table>

        <div class="slide-number">6</div>
    </div>

    <!-- Slide 7: Coupon & Growth Management -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">🎟️ Coupon & Growth Management</h1>
            <p class="slide-subtitle">Drive Customer Engagement & Revenue Growth</p>
        </div>

        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">🚀</span>
                <h3 class="feature-title">Venue Popularity Campaigns</h3>
                <p class="feature-description">
                    Create targeted discount campaigns to boost venue visibility
                    and attract new customers during off-peak hours.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📈</span>
                <h3 class="feature-title">Growth Initiatives</h3>
                <p class="feature-description">
                    Strategic promotional campaigns to increase booking frequency
                    and customer retention with measurable ROI tracking.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">💰</span>
                <h3 class="feature-title">Customer Refunds</h3>
                <p class="feature-description">
                    Flexible refund processing through coupon codes upon request
                    to Grid२Play, maintaining customer satisfaction.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📊</span>
                <h3 class="feature-title">Revenue Impact Tracking</h3>
                <p class="feature-description">
                    Comprehensive analytics on promotional campaign performance
                    with detailed revenue impact analysis.
                </p>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">🎯 Smart Coupon Strategy</h3>
            <p>Our coupon system is designed to drive long-term customer value, not just one-time discounts. Track customer acquisition costs and lifetime value through promotional campaigns.</p>
        </div>

        <div class="slide-number">7</div>
    </div>

    <!-- Slide 8: Support Infrastructure -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">🛠️ Support Infrastructure</h1>
            <p class="slide-subtitle">Dedicated Partner Success Team</p>
        </div>

        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">💬</span>
                <h3 class="feature-title">Multi-Channel Support</h3>
                <p class="feature-description">
                    • Live chat integration<br>
                    • WhatsApp business support<br>
                    • Phone call assistance<br>
                    • Email ticket system<br>
                    • In-app help center
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3 class="feature-title">Instant Response</h3>
                <p class="feature-description">
                    • Real-time chat responses<br>
                    • Priority venue admin support<br>
                    • Emergency contact system<br>
                    • 24/7 technical assistance<br>
                    • Dedicated account managers
                </p>
            </div>
        </div>

        <div class="process-flow">
            <div class="process-step">
                <div class="process-number">1</div>
                <h4 style="color: #10b981;">Issue Reported</h4>
                <p style="color: #cbd5e1;">Via any channel</p>
            </div>
            <div class="arrow">→</div>
            <div class="process-step">
                <div class="process-number">2</div>
                <h4 style="color: #10b981;">Instant Acknowledgment</h4>
                <p style="color: #cbd5e1;">Automated response</p>
            </div>
            <div class="arrow">→</div>
            <div class="process-step">
                <div class="process-number">3</div>
                <h4 style="color: #10b981;">Expert Resolution</h4>
                <p style="color: #cbd5e1;">Dedicated support team</p>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">🎯 Venue Admin Priority</h3>
            <p>As a venue partner, you get priority support with dedicated assistance for booking issues, payment queries, and technical problems. Our success depends on your success.</p>
        </div>

        <div class="slide-number">8</div>
    </div>

    <!-- Slide 9: Future Features -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">🚀 Future Features (Beta)</h1>
            <p class="slide-subtitle">Innovation Pipeline for 2025</p>
        </div>

        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">🏆</span>
                <h3 class="feature-title">Tournament Management</h3>
                <p class="feature-description">
                    Complete tournament hosting platform launching later this year.
                    Manage registrations, brackets, and prize distribution with
                    integrated revenue sharing opportunities.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🎮</span>
                <h3 class="feature-title">Challenge Mode (Unique USP)</h3>
                <p class="feature-description">
                    Revolutionary gamification feature allowing venue admins to create
                    competitive challenges between users. Drive repeat bookings through
                    community engagement with revenue sharing opportunities.
                </p>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">🎯 Challenge Mode Benefits</h3>
            <ul style="color: #cbd5e1; margin-left: 20px; line-height: 2;">
                <li>Create skill-based competitions between regular customers</li>
                <li>Automated leaderboards and ranking systems</li>
                <li>Prize pool management with venue revenue sharing</li>
                <li>Community building features to increase customer loyalty</li>
                <li>Social sharing integration for organic marketing</li>
                <li>Data analytics on customer engagement patterns</li>
            </ul>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">Q4</span>
                <div class="stat-label">Tournament System Launch</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">2025</span>
                <div class="stat-label">Challenge Mode Beta</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">30%</span>
                <div class="stat-label">Expected Booking Increase</div>
            </div>
        </div>

        <div class="slide-number">9</div>
    </div>

    <!-- Slide 10: Technical Architecture -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">⚙️ Technical Architecture</h1>
            <p class="slide-subtitle">Built for Scale & Reliability</p>
        </div>

        <div class="content-grid">
            <div class="feature-card">
                <span class="feature-icon">📱</span>
                <h3 class="feature-title">Mobile-First Design</h3>
                <p class="feature-description">
                    • 90% mobile user base optimization<br>
                    • 48px minimum touch targets<br>
                    • Dark emerald/black theme<br>
                    • Responsive design patterns<br>
                    • Progressive Web App features
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🔒</span>
                <h3 class="feature-title">Database-Driven Security</h3>
                <p class="feature-description">
                    • Dynamic platform fee calculations<br>
                    • Real-time TDS rate updates<br>
                    • Secure payment processing<br>
                    • Audit trail for all transactions<br>
                    • Role-based access control
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3 class="feature-title">Real-Time Updates</h3>
                <p class="feature-description">
                    • WebSocket connections for live data<br>
                    • Instant booking notifications<br>
                    • Live dashboard metrics<br>
                    • Real-time availability updates<br>
                    • Push notification system
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🔗</span>
                <h3 class="feature-title">Integration Ecosystem</h3>
                <p class="feature-description">
                    • MSG91 for WhatsApp/SMS<br>
                    • Razorpay payment gateway<br>
                    • Google Maps integration<br>
                    • Excel export functionality<br>
                    • API-first architecture
                </p>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 15px;">🏗️ Scalable Infrastructure</h3>
            <p>Built on modern cloud architecture with Supabase backend, ensuring 99.9% uptime, automatic scaling, and enterprise-grade security for your venue operations.</p>
        </div>

        <div class="slide-number">10</div>
    </div>

    <!-- Slide 11: Call to Action -->
    <div class="slide">
        <div class="logo">Grid२Play</div>
        <div class="slide-header">
            <h1 class="slide-title">🤝 Partner with Grid२Play</h1>
            <p class="slide-subtitle">Join India's Most Transparent Sports Booking Platform</p>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <span class="stat-number">5%</span>
                <div class="stat-label">Platform Fee (Transparent)</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">1%</span>
                <div class="stat-label">TDS Rate (Database-driven)</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">24/7</span>
                <div class="stat-label">WhatsApp Support</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">100%</span>
                <div class="stat-label">Revenue Transparency</div>
            </div>
        </div>

        <div class="highlight-box">
            <h3 style="color: #10b981; margin-bottom: 20px; text-align: center;">🎯 Why Choose Grid२Play?</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 30px;">
                <div style="text-align: center;">
                    <h4 style="color: #10b981; margin-bottom: 10px;">💰 Financial Transparency</h4>
                    <p style="color: #cbd5e1;">Every ₹1 accounted for with complete breakdown</p>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: #10b981; margin-bottom: 10px;">📱 Mobile-First</h4>
                    <p style="color: #cbd5e1;">Optimized for 90% mobile user base</p>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: #10b981; margin-bottom: 10px;">⚡ Real-Time</h4>
                    <p style="color: #cbd5e1;">Instant updates and notifications</p>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: #10b981; margin-bottom: 10px;">🛠️ Support</h4>
                    <p style="color: #cbd5e1;">Dedicated venue admin assistance</p>
                </div>
            </div>
        </div>

        <div class="cta-section">
            <h2 style="color: #10b981; margin-bottom: 20px;">Ready to Transform Your Venue Management?</h2>
            <p style="color: #cbd5e1; margin-bottom: 30px; font-size: 1.1rem;">
                Join hundreds of venue partners who trust Grid२Play for transparent,
                efficient, and profitable sports facility management.
            </p>

            <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                <a href="tel:+918448609110" class="cta-button">📞 Call: +91 84486 09110</a>
                <a href="https://wa.me/918448609110" class="cta-button">💬 WhatsApp: +91 84486 09110</a>
                <a href="mailto:<EMAIL>" class="cta-button">📧 Email: <EMAIL></a>
            </div>

            <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid rgba(16, 185, 129, 0.3);">
                <h3 style="color: #10b981; margin-bottom: 15px;">Next Steps:</h3>
                <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <p style="color: #cbd5e1; margin-bottom: 10px;">1. Schedule a demo call to see the admin interface</p>
                    <p style="color: #cbd5e1; margin-bottom: 10px;">2. Venue assessment and onboarding discussion</p>
                    <p style="color: #cbd5e1; margin-bottom: 10px;">3. Technical integration and training</p>
                    <p style="color: #cbd5e1; margin-bottom: 10px;">4. Go live with full support</p>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #64748b;">
            <p>Grid२Play - Empowering Sports Communities Through Technology</p>
            <p style="font-size: 0.9rem; margin-top: 10px;">© 2025 Grid२Play. All rights reserved.</p>
        </div>

        <div class="slide-number">11</div>
    </div>
</body>

<script>
// Collapsible Panel Functionality
function togglePanel(header) {
    const panel = header.parentElement;
    const content = panel.querySelector('.panel-content');
    const toggle = header.querySelector('.panel-toggle');

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        toggle.textContent = '+';
        toggle.style.transform = 'rotate(0deg)';
    } else {
        content.classList.add('expanded');
        toggle.textContent = '−';
        toggle.style.transform = 'rotate(180deg)';
    }
}

// Toast Notification Demo (for presentation purposes)
function showToast(message) {
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) return;

    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;

    toastContainer.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Demo button interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to step CTA buttons
    const stepButtons = document.querySelectorAll('.step-cta');
    stepButtons.forEach((button, index) => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const messages = [
                'Viewing real-time booking requests! 📋',
                'Booking modification interface loaded! ⚙️',
                'Customer notification sent via WhatsApp! 💬'
            ];
            showToast(messages[index] || 'Demo action completed! ✅');
        });
    });

    // Auto-expand first panel on reporting slide
    const firstPanel = document.querySelector('.collapsible-panel .panel-header');
    if (firstPanel) {
        setTimeout(() => {
            togglePanel(firstPanel);
        }, 1000);
    }
});

// Progress bar animation
function animateProgressBar() {
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        let width = 0;
        const interval = setInterval(() => {
            if (width >= 100) {
                clearInterval(interval);
            } else {
                width += 2;
                progressFill.style.width = width + '%';
            }
        }, 50);
    }
}

// Initialize animations when slide is visible
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting && entry.target.querySelector('.progress-fill')) {
            animateProgressBar();
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.slide');
    slides.forEach(slide => {
        observer.observe(slide);
    });
});
</script>

</html>
