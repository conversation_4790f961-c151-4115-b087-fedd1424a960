/**
 * Timezone utility functions for Grid२Play
 * Handles IST (Indian Standard Time) timezone conversions
 */

/**
 * Get current date and time in IST (UTC+5:30)
 * @returns Date object representing current IST time
 */
export const getISTDate = (): Date => {
  // Create a new date in IST timezone using proper timezone handling
  const now = new Date();

  // Get UTC time in milliseconds
  const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);

  // IST is UTC+5:30 (5.5 hours ahead of UTC)
  const istOffset = 5.5 * 60 * 60 * 1000;
  const istTime = new Date(utcTime + istOffset);

  return istTime;
};

/**
 * Format a date as YYYY-MM-DD string for HTML date inputs
 * @param date - Date object to format
 * @returns Formatted date string
 */
export const formatDateForInput = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Get current date in IST as YYYY-MM-DD string
 * @returns Current IST date as string
 */
export const getISTDateString = (): string => {
  const istDate = getISTDate();
  return formatDateForInput(istDate);
};

/**
 * Get minimum booking date (today in IST)
 * @returns Today's date in IST as YYYY-MM-DD string
 */
export const getMinBookingDate = (): string => {
  return getISTDateString();
};

/**
 * Get maximum booking date based on user role
 * @param userRole - User role ('admin', 'super_admin', or regular user)
 * @returns Maximum booking date as YYYY-MM-DD string
 */
export const getMaxBookingDate = (userRole: string | null): string => {
  const todayIST = getISTDate();
  const maxDays = userRole === 'admin' || userRole === 'super_admin' ? 30 : 14;
  const maxDate = new Date(todayIST);
  maxDate.setDate(todayIST.getDate() + maxDays);
  return formatDateForInput(maxDate);
};

/**
 * Validate if a booking date is valid
 * @param selectedDate - Date string in YYYY-MM-DD format
 * @param userRole - User role for determining max booking days
 * @returns Validation result with valid flag and message
 */
export const validateBookingDate = (
  selectedDate: string,
  userRole: string | null
): { valid: boolean; message: string } => {
  if (!selectedDate) return { valid: false, message: "Please select a date" };

  // Get current IST date and format it properly for comparison
  const todayIST = getISTDate();
  const todayISTString = formatDateForInput(todayIST);

  // Parse selected date as a date object for comparison
  const selectedDateObj = new Date(selectedDate + 'T00:00:00');
  const todayDateObj = new Date(todayISTString + 'T00:00:00');

  // Get max booking date
  const maxDateStr = getMaxBookingDate(userRole);
  const maxDateObj = new Date(maxDateStr + 'T00:00:00');

  // Compare dates (all normalized to midnight for date-only comparison)
  if (selectedDateObj < todayDateObj) {
    return { valid: false, message: "Cannot book slots in the past" };
  }

  if (selectedDateObj > maxDateObj) {
    const maxDays = userRole === 'admin' || userRole === 'super_admin' ? 30 : 14;
    const userType = userRole === 'admin' || userRole === 'super_admin' ? 'Admins' : 'Regular users';
    return {
      valid: false,
      message: `${userType} can book up to ${maxDays} days ahead`
    };
  }

  return { valid: true, message: "" };
};

/**
 * Check if current time is in the problematic timezone window
 * Between 12:00 AM to 5:30 AM IST, UTC date might be different
 * @returns true if in problematic window
 */
export const isInTimezoneProblematicWindow = (): boolean => {
  const istDate = getISTDate();
  const hour = istDate.getHours();
  const minute = istDate.getMinutes();
  
  // Between 12:00 AM (0:00) to 5:30 AM (5:30)
  return hour < 5 || (hour === 5 && minute <= 30);
};

/**
 * Convert UTC date to IST date string
 * @param utcDate - UTC date object
 * @returns IST date as YYYY-MM-DD string
 */
export const convertUTCToISTString = (utcDate: Date): string => {
  // Convert UTC date to IST using proper timezone handling
  const utcTime = utcDate.getTime();
  const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  const istTime = new Date(utcTime + istOffset);
  return formatDateForInput(istTime);
};

/**
 * Get current time in IST as HH:MM format
 * @returns Current IST time as string
 */
export const getCurrentISTTime = (): string => {
  const istDate = getISTDate();
  const hours = String(istDate.getHours()).padStart(2, '0');
  const minutes = String(istDate.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};
