import { supabase } from '@/integrations/supabase/client';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expires: number;
  accessCount: number;
  lastAccessed: number;
}

interface AvailabilitySlot {
  start_time: string;
  end_time: string;
  is_available: boolean;
  available_spots?: number;
  max_capacity?: number;
  price: string;
  booking_type: string;
}

interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
  persistToStorage: boolean;
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
  evictions: number;
}

class AvailabilityCache {
  private cache: Map<string, CacheEntry<AvailabilitySlot[]>>;
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer: NodeJS.Timeout | null;
  private storageKey = 'grid2play_availability_cache';

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 100,
      defaultTTL: 30000, // 30 seconds
      cleanupInterval: 60000, // 1 minute
      persistToStorage: true,
      ...config
    };

    this.cache = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      hitRate: 0,
      evictions: 0
    };

    this.initializeCache();
    this.startCleanupTimer();
  }

  private initializeCache(): void {
    if (this.config.persistToStorage && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(this.storageKey);
        if (stored) {
          const parsed = JSON.parse(stored);
          const now = Date.now();
          
          // Restore non-expired entries
          Object.entries(parsed).forEach(([key, entry]: [string, any]) => {
            if (entry.expires > now) {
              this.cache.set(key, entry);
            }
          });
        }
      } catch (error) {
        console.warn('Failed to restore cache from storage:', error);
      }
    }
  }

  private persistToStorage(): void {
    if (this.config.persistToStorage && typeof window !== 'undefined') {
      try {
        const cacheObject = Object.fromEntries(this.cache.entries());
        localStorage.setItem(this.storageKey, JSON.stringify(cacheObject));
      } catch (error) {
        console.warn('Failed to persist cache to storage:', error);
      }
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private cleanup(): void {
    const now = Date.now();
    let evicted = 0;

    // Remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.cache.delete(key);
        evicted++;
      }
    }

    // LRU eviction if cache is too large
    if (this.cache.size > this.config.maxSize) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
      
      const toEvict = entries.slice(0, this.cache.size - this.config.maxSize);
      toEvict.forEach(([key]) => {
        this.cache.delete(key);
        evicted++;
      });
    }

    this.stats.evictions += evicted;
    this.stats.size = this.cache.size;
    this.updateHitRate();

    if (evicted > 0) {
      this.persistToStorage();
    }
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private generateKey(courtId: string, date: string): string {
    return `${courtId}:${date}`;
  }

  public async get(courtId: string, date: string): Promise<AvailabilitySlot[] | null> {
    const key = this.generateKey(courtId, date);
    const entry = this.cache.get(key);
    const now = Date.now();

    if (entry && now < entry.expires) {
      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = now;
      this.stats.hits++;
      this.updateHitRate();
      
      return entry.data;
    }

    this.stats.misses++;
    this.updateHitRate();
    return null;
  }

  public set(
    courtId: string, 
    date: string, 
    data: AvailabilitySlot[], 
    ttl?: number
  ): void {
    const key = this.generateKey(courtId, date);
    const now = Date.now();
    const expires = now + (ttl || this.config.defaultTTL);

    const entry: CacheEntry<AvailabilitySlot[]> = {
      data,
      timestamp: now,
      expires,
      accessCount: 1,
      lastAccessed: now
    };

    this.cache.set(key, entry);
    this.stats.size = this.cache.size;

    // Trigger cleanup if needed
    if (this.cache.size > this.config.maxSize) {
      this.cleanup();
    } else {
      this.persistToStorage();
    }
  }

  public invalidate(courtId: string, date: string): void {
    const key = this.generateKey(courtId, date);
    if (this.cache.delete(key)) {
      this.stats.size = this.cache.size;
      this.persistToStorage();
    }
  }

  public invalidatePattern(pattern: string): number {
    let invalidated = 0;
    const regex = new RegExp(pattern);

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        invalidated++;
      }
    }

    if (invalidated > 0) {
      this.stats.size = this.cache.size;
      this.persistToStorage();
    }

    return invalidated;
  }

  public clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      hitRate: 0,
      evictions: 0
    };
    
    if (this.config.persistToStorage && typeof window !== 'undefined') {
      localStorage.removeItem(this.storageKey);
    }
  }

  public getStats(): CacheStats {
    return { ...this.stats };
  }

  public getSize(): number {
    return this.cache.size;
  }

  public has(courtId: string, date: string): boolean {
    const key = this.generateKey(courtId, date);
    const entry = this.cache.get(key);
    return entry ? Date.now() < entry.expires : false;
  }

  public preload(requests: Array<{ courtId: string; date: string }>): Promise<void[]> {
    const promises = requests.map(async ({ courtId, date }) => {
      if (!this.has(courtId, date)) {
        try {
          const { data, error } = await supabase.rpc('get_unified_availability', {
            p_court_id: courtId,
            p_date: date
          });

          if (error) throw error;

          const slots: AvailabilitySlot[] = (data || []).map((slot: any) => ({
            ...slot,
            price: slot.price || '0',
            booking_type: slot.booking_type || 'court_based'
          }));

          this.set(courtId, date, slots);
        } catch (error) {
          console.error(`Failed to preload availability for ${courtId}:${date}`, error);
        }
      }
    });

    return Promise.all(promises);
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }
}

// Singleton instance
let cacheInstance: AvailabilityCache | null = null;

export const getAvailabilityCache = (config?: Partial<CacheConfig>): AvailabilityCache => {
  if (!cacheInstance) {
    cacheInstance = new AvailabilityCache(config);
  }
  return cacheInstance;
};

// React hook for cache integration
export const useAvailabilityCache = (config?: Partial<CacheConfig>) => {
  const cache = getAvailabilityCache(config);

  const getCachedAvailability = async (
    courtId: string, 
    date: string
  ): Promise<AvailabilitySlot[]> => {
    // Try cache first
    const cached = await cache.get(courtId, date);
    if (cached) {
      return cached;
    }

    // Fetch from database
    try {
      const { data, error } = await supabase.rpc('get_unified_availability', {
        p_court_id: courtId,
        p_date: date
      });

      if (error) throw error;

      const slots: AvailabilitySlot[] = (data || []).map((slot: any) => ({
        ...slot,
        price: slot.price || '0',
        booking_type: slot.booking_type || 'court_based'
      }));

      // Cache the result
      cache.set(courtId, date, slots);
      return slots;
    } catch (error) {
      console.error('Failed to fetch availability:', error);
      throw error;
    }
  };

  const preloadAvailability = (requests: Array<{ courtId: string; date: string }>) => {
    return cache.preload(requests);
  };

  const invalidateAvailability = (courtId: string, date: string) => {
    cache.invalidate(courtId, date);
  };

  const getCacheStats = () => cache.getStats();

  return {
    getCachedAvailability,
    preloadAvailability,
    invalidateAvailability,
    getCacheStats,
    cache
  };
};

export default AvailabilityCache;
