/**
 * Grid२Play Excel Formatting Utility
 * Mobile-optimized Excel report formatting with emerald branding
 * Compatible with Android Excel, iOS Excel, and iOS Numbers apps
 */

import * as XLSX from 'xlsx';

// Grid२Play brand colors
export const GRID2PLAY_COLORS = {
  EMERALD_PRIMARY: '059669', // #059669 - Grid२Play emerald brand color
  EMERALD_LIGHT: '10B981',   // #10B981 - Lighter emerald for alternating rows
  WHITE: 'FFFFFF',           // #FFFFFF - White background
  BLACK: '000000',           // #000000 - Black text
  GRAY_LIGHT: 'F3F4F6',      // #F3F4F6 - Light gray for borders
  GRAY_DARK: '374151',       // #374151 - Dark gray for secondary text
};

// Mobile-optimized row heights (in Excel units)
export const MOBILE_ROW_HEIGHTS = {
  HEADER: 25,      // 25 units ≈ 48px touch target
  DATA: 22,        // 22 units ≈ 42px for data rows
  SUMMARY: 25,     // 25 units ≈ 48px for summary rows
  SECTION: 20,     // 20 units for section headers
};

// Mobile-optimized column widths
export const MOBILE_COLUMN_WIDTHS = {
  DATE: 12,        // Date columns
  NAME: 15,        // Name/text columns
  PHONE: 14,       // Phone number columns
  AMOUNT: 12,      // Currency amount columns
  REFERENCE: 16,   // Reference ID columns
  STATUS: 10,      // Status columns
  SHORT: 8,        // Short text columns
  MEDIUM: 12,      // Medium text columns
  LONG: 18,        // Long text columns
};

/**
 * Interface for Excel formatting options
 */
export interface ExcelFormattingOptions {
  sheetName: string;
  title: string;
  freezeHeaderRow?: boolean;
  mobileOptimized?: boolean;
  includeGridlines?: boolean;
}

/**
 * Interface for financial summary data
 */
export interface FinancialSummary {
  totalPlatformFee: number;
  totalTdsAmount: number;
  totalNetSettlement: number;
  totalCouponDiscount?: number;
  totalGrossRevenue: number;
  totalBookings: number;
}

/**
 * Create mobile-optimized Excel worksheet with Grid२Play branding
 */
export function createMobileOptimizedWorksheet(
  data: any[][],
  options: ExcelFormattingOptions
): XLSX.WorkSheet {
  // Create worksheet from array of arrays
  const ws = XLSX.utils.aoa_to_sheet(data);

  // Apply mobile-optimized formatting
  if (options.mobileOptimized) {
    applyMobileFormatting(ws, data);
  }

  // Apply Grid२Play branding
  applyGrid2PlayBranding(ws, data);

  // Set column widths for mobile compatibility
  setMobileColumnWidths(ws, data[0]?.length || 0);

  // Freeze header row if requested
  if (options.freezeHeaderRow) {
    freezeHeaderRow(ws);
  }

  return ws;
}

/**
 * Apply mobile-optimized formatting to worksheet
 */
function applyMobileFormatting(ws: XLSX.WorkSheet, data: any[][]): void {
  const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
  
  // Set row heights for mobile touch targets
  if (!ws['!rows']) ws['!rows'] = [];
  
  for (let row = range.s.r; row <= range.e.r; row++) {
    if (!ws['!rows'][row]) ws['!rows'][row] = {};
    
    // Determine row type and set appropriate height
    const rowData = data[row];
    if (rowData && isHeaderRow(rowData)) {
      ws['!rows'][row].hpt = MOBILE_ROW_HEIGHTS.HEADER;
    } else if (rowData && isSummaryRow(rowData)) {
      ws['!rows'][row].hpt = MOBILE_ROW_HEIGHTS.SUMMARY;
    } else {
      ws['!rows'][row].hpt = MOBILE_ROW_HEIGHTS.DATA;
    }
  }
}

/**
 * Apply Grid२Play emerald branding to worksheet
 */
function applyGrid2PlayBranding(ws: XLSX.WorkSheet, data: any[][]): void {
  const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
  
  for (let row = range.s.r; row <= range.e.r; row++) {
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      const cell = ws[cellAddress];
      
      if (!cell) continue;
      
      // Initialize cell style
      if (!cell.s) cell.s = {};
      
      const rowData = data[row];
      
      // Apply header styling
      if (rowData && isHeaderRow(rowData)) {
        cell.s = {
          ...cell.s,
          fill: {
            fgColor: { rgb: GRID2PLAY_COLORS.EMERALD_PRIMARY }
          },
          font: {
            bold: true,
            color: { rgb: GRID2PLAY_COLORS.WHITE },
            sz: 12
          },
          alignment: {
            horizontal: 'center',
            vertical: 'center'
          },
          border: {
            top: { style: 'thin', color: { rgb: GRID2PLAY_COLORS.GRAY_LIGHT } },
            bottom: { style: 'thin', color: { rgb: GRID2PLAY_COLORS.GRAY_LIGHT } },
            left: { style: 'thin', color: { rgb: GRID2PLAY_COLORS.GRAY_LIGHT } },
            right: { style: 'thin', color: { rgb: GRID2PLAY_COLORS.GRAY_LIGHT } }
          }
        };
      }
      // Apply summary row styling
      else if (rowData && isSummaryRow(rowData)) {
        cell.s = {
          ...cell.s,
          fill: {
            fgColor: { rgb: GRID2PLAY_COLORS.EMERALD_LIGHT }
          },
          font: {
            bold: true,
            color: { rgb: GRID2PLAY_COLORS.BLACK },
            sz: 11
          },
          alignment: {
            horizontal: 'left',
            vertical: 'center'
          }
        };
      }
      // Apply data row styling
      else {
        cell.s = {
          ...cell.s,
          font: {
            color: { rgb: GRID2PLAY_COLORS.BLACK },
            sz: 10
          },
          alignment: {
            horizontal: 'left',
            vertical: 'center'
          },
          border: {
            bottom: { style: 'thin', color: { rgb: GRID2PLAY_COLORS.GRAY_LIGHT } }
          }
        };
      }
    }
  }
}

/**
 * Set mobile-optimized column widths
 */
function setMobileColumnWidths(ws: XLSX.WorkSheet, columnCount: number): void {
  if (!ws['!cols']) ws['!cols'] = [];
  
  for (let col = 0; col < columnCount; col++) {
    if (!ws['!cols'][col]) ws['!cols'][col] = {};
    
    // Set default medium width, can be customized per column
    ws['!cols'][col].wch = MOBILE_COLUMN_WIDTHS.MEDIUM;
  }
}

/**
 * Freeze the header row for better mobile scrolling
 */
function freezeHeaderRow(ws: XLSX.WorkSheet): void {
  ws['!freeze'] = { xSplit: 0, ySplit: 1 };
}

/**
 * Check if a row is a header row
 */
function isHeaderRow(rowData: any[]): boolean {
  if (!rowData || rowData.length === 0) return false;
  
  const firstCell = String(rowData[0] || '').toUpperCase();
  
  // Check for common header patterns
  return (
    firstCell.includes('BOOKING DATE') ||
    firstCell.includes('SETTLEMENT REFERENCE') ||
    firstCell.includes('CUSTOMER NAME') ||
    firstCell.includes('VENUE') ||
    firstCell === 'DATE' ||
    firstCell === 'NAME' ||
    firstCell === 'PHONE' ||
    firstCell === 'COURT' ||
    firstCell === 'AMOUNT'
  );
}

/**
 * Check if a row is a summary/total row
 */
function isSummaryRow(rowData: any[]): boolean {
  if (!rowData || rowData.length === 0) return false;
  
  const firstCell = String(rowData[0] || '').toUpperCase();
  
  return (
    firstCell.includes('TOTAL') ||
    firstCell.includes('SUMMARY') ||
    firstCell.includes('===') ||
    firstCell === 'TOTALS' ||
    firstCell === 'GRAND TOTAL'
  );
}

/**
 * Add financial summary totals to Excel data
 */
export function addFinancialSummaryTotals(
  excelData: any[][],
  summary: FinancialSummary,
  startRowIndex: number
): any[][] {
  const summaryRows: any[][] = [
    // Empty row for spacing
    ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    
    // Summary section header
    ['=== FINANCIAL SUMMARY ===', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    
    // Financial totals
    ['Total Bookings', summary.totalBookings.toString(), '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['Total Gross Revenue (₹)', summary.totalGrossRevenue.toFixed(2), '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['Total Platform Fee (₹)', summary.totalPlatformFee.toFixed(2), '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['Total TDS Amount (₹)', summary.totalTdsAmount.toFixed(2), '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    ['Total Net Settlement (₹)', summary.totalNetSettlement.toFixed(2), '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']
  ];

  // Add coupon discount if applicable
  if (summary.totalCouponDiscount && summary.totalCouponDiscount > 0) {
    summaryRows.push(['Total Coupon Discount (₹)', summary.totalCouponDiscount.toFixed(2), '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']);
  }

  // Insert summary rows at the specified position
  excelData.splice(startRowIndex, 0, ...summaryRows);
  
  return excelData;
}

/**
 * Create and download Excel file with Grid२Play formatting
 */
export function downloadFormattedExcel(
  data: any[][],
  filename: string,
  options: ExcelFormattingOptions
): void {
  // Create formatted worksheet
  const ws = createMobileOptimizedWorksheet(data, options);
  
  // Create workbook
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, options.sheetName);
  
  // Download file
  XLSX.writeFile(wb, filename);
}
