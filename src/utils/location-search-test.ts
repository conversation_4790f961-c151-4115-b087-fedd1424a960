// Test utility for validating location search functionality
import { googleMapsService } from './google-maps';


export interface LocationSearchTestResult {
  query: string;
  googleMapsResults: number;
  geocodingResults: number;
  googleMapsSuccess: boolean;
  geocodingSuccess: boolean;
  totalTime: number;
}

export class LocationSearchTester {
  // Test queries for Delhi NCR validation
  private testQueries = [
    'Connaught Place',
    'South Delhi',
    'Noida Sector 18',
    'Gurgaon Cyber City',
    'Karol Bagh',
    'Khan Market',
    'Dwarka',
    'Rohini',
    'Faridabad',
    'Ghaziabad'
  ];

  // Run comprehensive location search tests
  async runTests(): Promise<LocationSearchTestResult[]> {
    // Test logging removed for production security
    const results: LocationSearchTestResult[] = [];

    for (const query of this.testQueries) {
      const result = await this.testSingleQuery(query);
      results.push(result);

      // Add delay between tests to respect rate limits
      await this.delay(500);
    }

    this.logTestSummary(results);
    return results;
  }

  // Test a single query against both services
  private async testSingleQuery(query: string): Promise<LocationSearchTestResult> {
    // Query testing logging removed for production security
    const startTime = Date.now();

    let googleMapsResults = 0;
    let geocodingResults = 0;
    let googleMapsSuccess = false;
    let geocodingSuccess = false;

    // Test Google Maps
    try {
      const gmResults = await googleMapsService.searchAddresses(query);
      googleMapsResults = gmResults.length;
      googleMapsSuccess = true;
      // Results logging removed for production security
    } catch (error) {
      // Error logging removed for production security
    }

    // Test geocoding fallback
    try {
      const delhiQuery = `${query}, New Delhi, India`;
      const geoResults = await geocoding.searchAddresses(delhiQuery);
      
      // Apply Delhi filtering
      const filteredResults = geoResults.filter(suggestion => {
        const displayName = suggestion.display_name?.toLowerCase() || '';
        const city = suggestion.city?.toLowerCase() || '';
        const area = suggestion.area?.toLowerCase() || '';
        const state = suggestion.state?.toLowerCase() || '';
        
        return (
          city.includes('delhi') || 
          city.includes('new delhi') ||
          area.includes('delhi') ||
          area.includes('new delhi') ||
          displayName.includes('delhi') ||
          displayName.includes('new delhi') ||
          displayName.includes('gurgaon') ||
          displayName.includes('gurugram') ||
          displayName.includes('noida') ||
          displayName.includes('faridabad') ||
          displayName.includes('ghaziabad') ||
          state.includes('delhi')
        ) && suggestion.country?.toLowerCase().includes('india');
      });

      geocodingResults = filteredResults.length;
      geocodingSuccess = true;
      // Results logging removed for production security
    } catch (error) {
      // Error logging removed for production security
    }

    const totalTime = Date.now() - startTime;

    return {
      query,
      googleMapsResults,
      geocodingResults,
      googleMapsSuccess,
      geocodingSuccess,
      totalTime
    };
  }

  // Log test summary
  private logTestSummary(_results: LocationSearchTestResult[]): void {
    // Test summary logging removed for production security
    // All statistics and performance logging removed for production security
  }

  // Utility delay function
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Test API availability
  async testAPIAvailability(): Promise<{ googleMaps: boolean; geocoding: boolean }> {
    // API availability logging removed for production security

    let googleMaps = false;
    let geocoding = false;

    try {
      await googleMapsService.loadGoogleMapsAPI();
      googleMaps = googleMapsService.isAPILoaded();
      // API status logging removed for production security
    } catch (error) {
      // API error logging removed for production security
    }

    // Geocoding API removed - using Google Maps only
    geocoding = false;
    // API status logging removed for production security

    return { googleMaps, geocoding };
  }
}

// Export singleton instance
export const locationSearchTester = new LocationSearchTester();

// Console helper for manual testing
if (typeof window !== 'undefined') {
  (window as any).testLocationSearch = locationSearchTester;
}
