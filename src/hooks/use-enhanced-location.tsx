
import { useState, useEffect, useCallback } from 'react';
import { LocationState, LocationData, Coordinates } from '@/types/location';
import { locationStorage } from '@/utils/locationStorage';

import { toast } from '@/hooks/use-toast';

export function useEnhancedLocation() {
  const [state, setState] = useState<LocationState>({
    data: null,
    isLoading: true,
    error: null,
    hasPermission: null,
    isRefreshing: false
  });

  // Check permission status
  const checkPermissionStatus = useCallback(async () => {
    if (!navigator.permissions) {
      return 'prompt';
    }

    try {
      const result = await navigator.permissions.query({ name: 'geolocation' });
      return result.state;
    } catch {
      return 'prompt';
    }
  }, []);

  // IP location detection removed - Grid२Play only uses GPS and user preference locations

  // Get high-accuracy GPS location
  const getGPSLocation = useCallback(async (): Promise<LocationData | null> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve(null);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const coordinates: Coordinates = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            };

            // Create basic address from coordinates (no reverse geocoding needed)
            const address = {
              display_name: `${coordinates.latitude.toFixed(4)}, ${coordinates.longitude.toFixed(4)}`,
              area: 'Current location',
              city: 'Delhi NCR'
            };

            const locationData: LocationData = {
              coordinates,
              address,
              source: 'gps',
              timestamp: Date.now(),
              accuracy: position.coords.accuracy
            };

            resolve(locationData);
          } catch (error) {
            // GPS location processing failed - error details removed for production security
            resolve(null);
          }
        },
        () => {
          // GPS location failed - error details removed for production security
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  }, []);

  // Multi-layered location detection
  const detectLocation = useCallback(async (force = false) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Try cached location first (if not forcing refresh)
      if (!force) {
        const cached = locationStorage.load();
        if (cached) {
          setState(prev => ({
            ...prev,
            data: cached,
            isLoading: false,
            hasPermission: true
          }));
          return cached;
        }
      }

      // Check permission status
      const permissionStatus = await checkPermissionStatus();
      setState(prev => ({ ...prev, hasPermission: permissionStatus === 'granted' }));

      let locationData: LocationData | null = null;

      // Only try GPS location - no IP fallback
      if (permissionStatus === 'granted' || permissionStatus === 'prompt') {
        locationData = await getGPSLocation();

        // GPS location detected - no toast needed for automatic detection
      }

      if (locationData) {
        locationStorage.save(locationData);
        setState(prev => ({
          ...prev,
          data: locationData,
          isLoading: false,
          error: null
        }));
        return locationData;
      } else {
        throw new Error('Unable to detect location');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Location detection failed';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));

      // Toast message removed - silent location detection failure

      return null;
    }
  }, [checkPermissionStatus, getGPSLocation]);

  // Refresh location
  const refreshLocation = useCallback(async () => {
    setState(prev => ({ ...prev, isRefreshing: true }));
    await detectLocation(true);
    setState(prev => ({ ...prev, isRefreshing: false }));
  }, [detectLocation]);

  // Set manual location (simplified - no geocoding needed)
  const setManualLocation = useCallback(async (address: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Create basic location data without geocoding
      const locationData: LocationData = {
        coordinates: null, // No coordinates for manual address
        address: {
          display_name: address,
          area: address,
          city: 'Delhi NCR'
        },
        source: 'manual',
        timestamp: Date.now()
      };

      locationStorage.save(locationData);
      setState(prev => ({
        ...prev,
        data: locationData,
        isLoading: false,
        error: null
      }));

      toast({
        title: "Location set",
        description: `Location set to: ${address}`,
      });

      return locationData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to set location';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));

      toast({
        title: "Failed to set location",
        description: errorMessage,
        variant: "destructive"
      });

      return null;
    }
  }, []);

  // Clear location
  const clearLocation = useCallback(() => {
    locationStorage.clear();
    setState({
      data: null,
      isLoading: false,
      error: null,
      hasPermission: null,
      isRefreshing: false
    });
  }, []);

  // Auto-detect on mount
  useEffect(() => {
    detectLocation();
  }, []);

  return {
    ...state,
    detectLocation,
    refreshLocation,
    setManualLocation,
    clearLocation
  };
}

// Export distance calculation function for compatibility
export function calculateDistance(
  lat1?: number | null,
  lon1?: number | null,
  lat2?: number | null,
  lon2?: number | null
): number | null {
  if (!lat1 || !lon1 || !lat2 || !lon2) return null;
  
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2); 
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
  const distance = R * c; // Distance in km
  
  return distance;
}
