import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { getISTDateString, validateBookingDate } from '@/utils/timezone';

interface SmartDefaults {
  venue?: string;
  sport?: string;
  court?: string;
  date?: string;
  confidence: {
    venue: number;
    sport: number;
    court: number;
    date: number;
  };
  reasons: {
    venue?: string;
    sport?: string;
    court?: string;
    date?: string;
  };
}

interface UserBookingPattern {
  venue_id: string;
  sport_id: string;
  court_id: string;
  preferred_times: string[];
  frequency: number;
  last_booked: string;
}

interface LocationData {
  latitude?: number;
  longitude?: number;
  city?: string;
  area?: string;
}

export const useSmartBookingDefaults = () => {
  const { user } = useAuth();
  const [smartDefaults, setSmartDefaults] = useState<SmartDefaults>({
    confidence: { venue: 0, sport: 0, court: 0, date: 0 },
    reasons: {}
  });
  const [loading, setLoading] = useState(false);
  const [userLocation, setUserLocation] = useState<LocationData>({});

  // Get user's location (with permission)
  const getUserLocation = useCallback(async (): Promise<LocationData> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve({});
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        () => {
          // Fallback to IP-based location or profile location
          resolve({});
        },
        { timeout: 5000, enableHighAccuracy: false }
      );
    });
  }, []);

  // Analyze user's booking patterns
  const analyzeBookingPatterns = useCallback(async (): Promise<UserBookingPattern[]> => {
    if (!user) return [];

    try {
      const { data: bookings, error } = await supabase
        .from('bookings')
        .select(`
          id,
          court_id,
          booking_date,
          start_time,
          end_time,
          created_at,
          courts (
            id,
            name,
            venue_id,
            sport_id,
            venues (
              id,
              name,
              location,
              latitude,
              longitude
            ),
            sports (
              id,
              name
            )
          )
        `)
        .eq('user_id', user.id)
        .eq('status', 'confirmed')
        .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()) // Last 90 days
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      // Group bookings by venue-sport combination
      const patterns = bookings?.reduce((acc, booking) => {
        const court = booking.courts;
        if (!court) return acc;

        const key = `${court.venue_id}-${court.sport_id}`;
        if (!acc[key]) {
          acc[key] = {
            venue_id: court.venue_id,
            sport_id: court.sport_id,
            court_id: court.id,
            preferred_times: [],
            frequency: 0,
            last_booked: booking.created_at,
            venue_name: court.venues?.name,
            sport_name: court.sports?.name,
            venue_location: court.venues?.location
          };
        }

        acc[key].frequency += 1;
        acc[key].preferred_times.push(booking.start_time);
        
        // Update last booked if more recent
        if (new Date(booking.created_at) > new Date(acc[key].last_booked)) {
          acc[key].last_booked = booking.created_at;
        }

        return acc;
      }, {} as Record<string, any>) || {};

      return Object.values(patterns).sort((a: any, b: any) => b.frequency - a.frequency);
    } catch (error) {
      console.error('Error analyzing booking patterns:', error);
      return [];
    }
  }, [user]);

  // Find nearest venues based on location
  const findNearestVenues = useCallback(async (location: LocationData): Promise<any[]> => {
    if (!location.latitude || !location.longitude) return [];

    try {
      const { data: venues, error } = await supabase
        .from('venues')
        .select('id, name, location, latitude, longitude')
        .eq('is_active', true)
        .not('latitude', 'is', null)
        .not('longitude', 'is', null);

      if (error) throw error;

      // Calculate distances and sort by proximity
      const venuesWithDistance = venues?.map(venue => {
        const distance = calculateDistance(
          location.latitude!,
          location.longitude!,
          venue.latitude,
          venue.longitude
        );
        return { ...venue, distance };
      }).sort((a, b) => a.distance - b.distance) || [];

      return venuesWithDistance.slice(0, 5); // Top 5 nearest venues
    } catch (error) {
      console.error('Error finding nearest venues:', error);
      return [];
    }
  }, []);

  // Calculate distance between two coordinates (Haversine formula)
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Get user's profile preferences
  const getUserPreferences = useCallback(async () => {
    if (!user) return {};

    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('preferred_location, favorite_sport, preferred_venue_id')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return profile || {};
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      return {};
    }
  }, [user]);

  // Determine optimal date based on patterns and availability
  const getOptimalDate = useCallback(async (patterns: UserBookingPattern[]): Promise<{ date: string; confidence: number; reason: string }> => {
    const today = getISTDateString();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // Check if user typically books for today or future dates
    const todayValidation = validateBookingDate(today);
    
    if (todayValidation.valid) {
      // Check if user has patterns of same-day booking
      const sameDayBookings = patterns.filter(pattern => {
        const daysDiff = Math.floor((Date.now() - new Date(pattern.last_booked).getTime()) / (1000 * 60 * 60 * 24));
        return daysDiff === 0;
      });

      if (sameDayBookings.length > 0) {
        return {
          date: today,
          confidence: 0.8,
          reason: 'You often book for today'
        };
      }
    }

    // Default to tomorrow
    return {
      date: tomorrowStr,
      confidence: 0.6,
      reason: 'Next available date'
    };
  }, []);

  // Main function to calculate smart defaults
  const calculateSmartDefaults = useCallback(async (): Promise<SmartDefaults> => {
    if (!user) {
      return {
        confidence: { venue: 0, sport: 0, court: 0, date: 0 },
        reasons: {}
      };
    }

    setLoading(true);

    try {
      // Parallel data fetching
      const [location, patterns, preferences, nearestVenues] = await Promise.all([
        getUserLocation(),
        analyzeBookingPatterns(),
        getUserPreferences(),
        getUserLocation().then(loc => findNearestVenues(loc))
      ]);

      setUserLocation(location);

      const defaults: SmartDefaults = {
        confidence: { venue: 0, sport: 0, court: 0, date: 0 },
        reasons: {}
      };

      // 1. Venue Selection Logic
      if (patterns.length > 0) {
        // Most frequent venue from booking history
        const topVenue = patterns[0];
        defaults.venue = topVenue.venue_id;
        defaults.confidence.venue = Math.min(0.9, topVenue.frequency / 10);
        defaults.reasons.venue = `Your most visited venue (${topVenue.frequency} bookings)`;
      } else if (preferences.preferred_venue_id) {
        // Profile preference
        defaults.venue = preferences.preferred_venue_id;
        defaults.confidence.venue = 0.7;
        defaults.reasons.venue = 'Your preferred venue';
      } else if (nearestVenues.length > 0) {
        // Nearest venue by location
        defaults.venue = nearestVenues[0].id;
        defaults.confidence.venue = 0.5;
        defaults.reasons.venue = `Nearest venue (${nearestVenues[0].distance.toFixed(1)}km away)`;
      }

      // 2. Sport Selection Logic
      if (patterns.length > 0) {
        // Most frequent sport
        const topSport = patterns[0];
        defaults.sport = topSport.sport_id;
        defaults.confidence.sport = Math.min(0.9, topSport.frequency / 8);
        defaults.reasons.sport = `Your most played sport (${topSport.frequency} bookings)`;
      } else if (preferences.favorite_sport) {
        // Profile preference
        defaults.sport = preferences.favorite_sport;
        defaults.confidence.sport = 0.7;
        defaults.reasons.sport = 'Your favorite sport';
      }

      // 3. Court Selection Logic (if venue and sport are determined)
      if (defaults.venue && defaults.sport && patterns.length > 0) {
        const matchingPattern = patterns.find(p => 
          p.venue_id === defaults.venue && p.sport_id === defaults.sport
        );
        if (matchingPattern) {
          defaults.court = matchingPattern.court_id;
          defaults.confidence.court = 0.8;
          defaults.reasons.court = 'Your usual court at this venue';
        }
      }

      // 4. Date Selection Logic
      const optimalDate = await getOptimalDate(patterns);
      defaults.date = optimalDate.date;
      defaults.confidence.date = optimalDate.confidence;
      defaults.reasons.date = optimalDate.reason;

      return defaults;

    } catch (error) {
      console.error('Error calculating smart defaults:', error);
      return {
        confidence: { venue: 0, sport: 0, court: 0, date: 0 },
        reasons: {}
      };
    } finally {
      setLoading(false);
    }
  }, [user, getUserLocation, analyzeBookingPatterns, getUserPreferences, findNearestVenues, getOptimalDate]);

  // Initialize smart defaults
  useEffect(() => {
    if (user) {
      calculateSmartDefaults().then(setSmartDefaults);
    }
  }, [user, calculateSmartDefaults]);

  // Refresh smart defaults
  const refreshDefaults = useCallback(() => {
    if (user) {
      calculateSmartDefaults().then(setSmartDefaults);
    }
  }, [user, calculateSmartDefaults]);

  return {
    smartDefaults,
    loading,
    userLocation,
    refreshDefaults,
    calculateSmartDefaults
  };
};

export default useSmartBookingDefaults;
