import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useEnhancedLocation } from '@/hooks/use-enhanced-location';
import { locationPreferenceService, UserLocationPreference } from '@/services/location-preference';

export type LocationSource = 'url' | 'preference' | 'gps' | 'none';

export interface SmartLocationData {
  coordinates: { latitude: number; longitude: number } | null;
  name: string | null;
  source: LocationSource;
  isLoading: boolean;
  error: string | null;
  userPreference: UserLocationPreference | null;
}

export interface SmartLocationParams {
  lat?: string;
  lng?: string;
  location?: string;
}

export function useSmartLocation(urlParams?: SmartLocationParams) {
  const { user } = useAuth();
  const { data: gpsLocationData, isLoading: gpsLoading } = useEnhancedLocation();

  // Memoize urlParams to prevent unnecessary re-renders
  const stableUrlParams = useMemo(() => {
    if (!urlParams) return undefined;
    return {
      lat: urlParams.lat,
      lng: urlParams.lng,
      location: urlParams.location
    };
  }, [
    urlParams?.lat,
    urlParams?.lng,
    urlParams?.location
  ]);

  const [smartLocation, setSmartLocation] = useState<SmartLocationData>({
    coordinates: null,
    name: null,
    source: 'none',
    isLoading: true,
    error: null,
    userPreference: null
  });

  const resolveLocation = useCallback(async () => {
    try {
      setSmartLocation(prev => ({ ...prev, isLoading: true, error: null }));

      // 1. URL parameters (highest priority - temporary override)
      if (stableUrlParams?.lat && stableUrlParams?.lng && stableUrlParams?.location) {
        const lat = parseFloat(stableUrlParams.lat);
        const lng = parseFloat(stableUrlParams.lng);

        // Validate coordinates
        if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
          // URL parameters with coordinates logging removed for production security

          setSmartLocation({
            coordinates: {
              latitude: lat,
              longitude: lng
            },
            name: decodeURIComponent(stableUrlParams.location),
            source: 'url',
            isLoading: false,
            error: null,
            userPreference: null
          });
          return;
        } else {
          // Invalid coordinates from URL parameters - coordinate details removed for production security
        }
      }

      // 2. User's saved preference (if authenticated)
      let userPreference: UserLocationPreference | null = null;
      if (user) {
        try {
          userPreference = await locationPreferenceService.getLocationPreference(user.id);
          if (userPreference) {
            setSmartLocation({
              coordinates: {
                latitude: userPreference.latitude,
                longitude: userPreference.longitude
              },
              name: userPreference.name,
              source: 'preference',
              isLoading: false,
              error: null,
              userPreference
            });
            return;
          }
        } catch (error) {
          // Failed to fetch user location preference - error details removed for production security
        }
      }

      // 3. GPS location (if available and enabled) - ONLY GPS, not IP fallback
      if (gpsLocationData?.coordinates && gpsLocationData.source === 'gps') {
        // Check if user has disabled location detection
        if (user && userPreference?.detectionEnabled === false) {
          // User has disabled GPS detection, skip to no location
        } else {
          setSmartLocation({
            coordinates: gpsLocationData.coordinates,
            name: gpsLocationData.address.area || gpsLocationData.address.city || 'Current location',
            source: 'gps',
            isLoading: false,
            error: null,
            userPreference
          });
          return;
        }
      }

      // 4. No location available
      setSmartLocation({
        coordinates: null,
        name: null,
        source: 'none',
        isLoading: false,
        error: null,
        userPreference
      });

    } catch (error) {
      setSmartLocation({
        coordinates: null,
        name: null,
        source: 'none',
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to resolve location',
        userPreference: null
      });
    }
  }, [user, gpsLocationData, stableUrlParams]);

  useEffect(() => {
    resolveLocation();
  }, [resolveLocation]);

  // Listen for location preference changes from other components
  useEffect(() => {
    const handleLocationPreferenceChange = () => {
      resolveLocation();
    };

    window.addEventListener('locationPreferenceChanged', handleLocationPreferenceChange);

    return () => {
      window.removeEventListener('locationPreferenceChanged', handleLocationPreferenceChange);
    };
  }, [resolveLocation]);

  const refreshLocation = useCallback(() => {
    resolveLocation();
  }, [resolveLocation]);

  const clearUserPreference = useCallback(async () => {
    if (!user) return;
    
    try {
      await locationPreferenceService.clearLocationPreference(user.id);
      await resolveLocation(); // Refresh after clearing
    } catch (error) {
      // Failed to clear location preference - error details removed for production security
    }
  }, [user, resolveLocation]);

  const updateDetectionSettings = useCallback(async (enabled: boolean) => {
    if (!user) return;
    
    try {
      await locationPreferenceService.updateDetectionSettings(user.id, enabled);
      await resolveLocation(); // Refresh after updating settings
    } catch (error) {
      // Failed to update detection settings - error details removed for production security
    }
  }, [user, resolveLocation]);

  return {
    ...smartLocation,
    refreshLocation,
    clearUserPreference,
    updateDetectionSettings,
    isGpsLoading: gpsLoading
  };
}
