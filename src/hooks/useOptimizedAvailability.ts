import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';

interface AvailabilitySlot {
  start_time: string;
  end_time: string;
  is_available: boolean;
  available_spots?: number;
  max_capacity?: number;
  price: string;
  booking_type: string;
}

interface AvailabilityCacheEntry {
  data: AvailabilitySlot[];
  timestamp: number;
  expires: number;
  courtId: string;
  date: string;
}

interface BatchAvailabilityRequest {
  courtId: string;
  date: string;
  priority: 'high' | 'medium' | 'low';
}

interface OptimizedAvailabilityHook {
  getAvailability: (courtId: string, date: string) => Promise<AvailabilitySlot[]>;
  preloadAvailability: (requests: BatchAvailabilityRequest[]) => Promise<void>;
  clearCache: () => void;
  getCacheStats: () => { hits: number; misses: number; size: number };
  loading: boolean;
  error: string | null;
}

const CACHE_DURATION = 30000; // 30 seconds
const MAX_CACHE_SIZE = 50; // Maximum cached entries
const BATCH_SIZE = 5; // Maximum concurrent requests
const DEBOUNCE_DELAY = 300; // Debounce delay for rapid requests

export const useOptimizedAvailability = (): OptimizedAvailabilityHook => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cache management
  const cache = useRef<Map<string, AvailabilityCacheEntry>>(new Map());
  const cacheStats = useRef({ hits: 0, misses: 0 });
  const pendingRequests = useRef<Map<string, Promise<AvailabilitySlot[]>>>(new Map());
  const requestQueue = useRef<BatchAvailabilityRequest[]>([]);
  const processingQueue = useRef(false);

  // Real-time subscription management
  const subscriptions = useRef<Map<string, any>>(new Map());
  const debounceTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Generate cache key
  const getCacheKey = useCallback((courtId: string, date: string): string => {
    return `${courtId}-${date}`;
  }, []);

  // Clean expired cache entries
  const cleanExpiredCache = useCallback(() => {
    const now = Date.now();
    const entries = Array.from(cache.current.entries());
    
    entries.forEach(([key, entry]) => {
      if (now > entry.expires) {
        cache.current.delete(key);
      }
    });

    // Limit cache size
    if (cache.current.size > MAX_CACHE_SIZE) {
      const sortedEntries = entries
        .filter(([, entry]) => now <= entry.expires)
        .sort(([, a], [, b]) => b.timestamp - a.timestamp);
      
      cache.current.clear();
      sortedEntries.slice(0, MAX_CACHE_SIZE).forEach(([key, entry]) => {
        cache.current.set(key, entry);
      });
    }
  }, []);

  // Fetch availability from database
  const fetchAvailabilityFromDB = useCallback(async (
    courtId: string, 
    date: string
  ): Promise<AvailabilitySlot[]> => {
    try {
      const { data, error } = await supabase.rpc('get_unified_availability', {
        p_court_id: courtId,
        p_date: date
      });

      if (error) throw error;

      return (data || []).map((slot: any) => ({
        ...slot,
        price: slot.price || '0',
        booking_type: slot.booking_type || 'court_based'
      }));
    } catch (err) {
      console.error('Database availability fetch error:', err);
      throw new Error('Failed to fetch availability from database');
    }
  }, []);

  // Get availability with caching
  const getAvailability = useCallback(async (
    courtId: string, 
    date: string
  ): Promise<AvailabilitySlot[]> => {
    const cacheKey = getCacheKey(courtId, date);
    
    // Check cache first
    const cached = cache.current.get(cacheKey);
    if (cached && Date.now() < cached.expires) {
      cacheStats.current.hits++;
      return cached.data;
    }

    cacheStats.current.misses++;

    // Check if request is already pending
    const pendingRequest = pendingRequests.current.get(cacheKey);
    if (pendingRequest) {
      return pendingRequest;
    }

    // Create new request
    const request = fetchAvailabilityFromDB(courtId, date)
      .then(data => {
        // Cache the result
        cache.current.set(cacheKey, {
          data,
          timestamp: Date.now(),
          expires: Date.now() + CACHE_DURATION,
          courtId,
          date
        });

        // Clean expired entries
        cleanExpiredCache();

        return data;
      })
      .catch(err => {
        setError(err.message);
        throw err;
      })
      .finally(() => {
        pendingRequests.current.delete(cacheKey);
      });

    pendingRequests.current.set(cacheKey, request);
    return request;
  }, [getCacheKey, fetchAvailabilityFromDB, cleanExpiredCache]);

  // Process batch requests
  const processBatchQueue = useCallback(async () => {
    if (processingQueue.current || requestQueue.current.length === 0) {
      return;
    }

    processingQueue.current = true;
    setLoading(true);

    try {
      // Sort by priority and take batch
      const sortedQueue = requestQueue.current.sort((a, b) => {
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

      const batch = sortedQueue.splice(0, BATCH_SIZE);
      
      // Process batch concurrently
      const promises = batch.map(({ courtId, date }) => 
        getAvailability(courtId, date).catch(err => {
          console.error(`Batch request failed for ${courtId}-${date}:`, err);
          return [];
        })
      );

      await Promise.all(promises);
    } catch (err) {
      console.error('Batch processing error:', err);
    } finally {
      processingQueue.current = false;
      setLoading(false);
      
      // Process remaining queue if any
      if (requestQueue.current.length > 0) {
        setTimeout(processBatchQueue, 100);
      }
    }
  }, [getAvailability]);

  // Preload availability for multiple courts/dates
  const preloadAvailability = useCallback(async (
    requests: BatchAvailabilityRequest[]
  ): Promise<void> => {
    // Filter out already cached requests
    const uncachedRequests = requests.filter(({ courtId, date }) => {
      const cacheKey = getCacheKey(courtId, date);
      const cached = cache.current.get(cacheKey);
      return !cached || Date.now() >= cached.expires;
    });

    if (uncachedRequests.length === 0) return;

    // Add to queue
    requestQueue.current.push(...uncachedRequests);
    
    // Process queue
    processBatchQueue();
  }, [getCacheKey, processBatchQueue]);

  // Setup real-time subscriptions with debouncing
  const setupRealtimeSubscription = useCallback((courtId: string, date: string) => {
    const subscriptionKey = getCacheKey(courtId, date);
    
    // Skip if already subscribed
    if (subscriptions.current.has(subscriptionKey)) {
      return;
    }

    try {
      const channel = supabase
        .channel(`availability-${subscriptionKey}`)
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'bookings',
          filter: `court_id=eq.${courtId},booking_date=eq.${date}`
        }, (payload) => {
          // Debounce cache invalidation
          const existingTimer = debounceTimers.current.get(subscriptionKey);
          if (existingTimer) {
            clearTimeout(existingTimer);
          }

          const timer = setTimeout(() => {
            // Invalidate cache
            cache.current.delete(subscriptionKey);
            
            // Refresh availability
            getAvailability(courtId, date).catch(err => {
              console.error('Real-time refresh error:', err);
            });
            
            debounceTimers.current.delete(subscriptionKey);
          }, DEBOUNCE_DELAY);

          debounceTimers.current.set(subscriptionKey, timer);
        })
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'blocked_slots',
          filter: `court_id=eq.${courtId},date=eq.${date}`
        }, (payload) => {
          // Same debounced invalidation for blocked slots
          const existingTimer = debounceTimers.current.get(subscriptionKey);
          if (existingTimer) {
            clearTimeout(existingTimer);
          }

          const timer = setTimeout(() => {
            cache.current.delete(subscriptionKey);
            getAvailability(courtId, date).catch(err => {
              console.error('Real-time refresh error:', err);
            });
            debounceTimers.current.delete(subscriptionKey);
          }, DEBOUNCE_DELAY);

          debounceTimers.current.set(subscriptionKey, timer);
        })
        .subscribe((status) => {
          if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.warn(`Subscription ${subscriptionKey} failed:`, status);
            subscriptions.current.delete(subscriptionKey);
          }
        });

      subscriptions.current.set(subscriptionKey, channel);
    } catch (err) {
      console.error('Failed to setup real-time subscription:', err);
    }
  }, [getCacheKey, getAvailability]);

  // Cleanup subscriptions
  const cleanupSubscriptions = useCallback(() => {
    subscriptions.current.forEach((channel, key) => {
      try {
        channel.unsubscribe();
      } catch (err) {
        console.error(`Failed to unsubscribe from ${key}:`, err);
      }
    });
    subscriptions.current.clear();

    // Clear debounce timers
    debounceTimers.current.forEach(timer => clearTimeout(timer));
    debounceTimers.current.clear();
  }, []);

  // Clear cache
  const clearCache = useCallback(() => {
    cache.current.clear();
    pendingRequests.current.clear();
    requestQueue.current = [];
    cacheStats.current = { hits: 0, misses: 0 };
    setError(null);
  }, []);

  // Get cache statistics
  const getCacheStats = useCallback(() => ({
    hits: cacheStats.current.hits,
    misses: cacheStats.current.misses,
    size: cache.current.size
  }), []);

  // Setup real-time subscriptions for cached entries
  useEffect(() => {
    const interval = setInterval(() => {
      cache.current.forEach((entry, key) => {
        if (Date.now() < entry.expires) {
          setupRealtimeSubscription(entry.courtId, entry.date);
        }
      });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [setupRealtimeSubscription]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupSubscriptions();
      debounceTimers.current.forEach(timer => clearTimeout(timer));
    };
  }, [cleanupSubscriptions]);

  return {
    getAvailability,
    preloadAvailability,
    clearCache,
    getCacheStats,
    loading,
    error
  };
};
