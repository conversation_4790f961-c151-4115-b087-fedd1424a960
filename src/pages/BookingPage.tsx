import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Check, CreditCard, Loader, MapPin, Activity, Calendar, Clock, ChevronDown, ChevronUp, Zap, Star, RefreshCw, X } from 'lucide-react';

// Import optimized components and hooks
import BookingSuccessModal from '@/components/BookingSuccessModal';
import { useIsMobile } from '@/hooks/use-mobile';
import useSmartBookingDefaults from '@/hooks/useSmartBookingDefaults';
import { useAvailabilityCache } from '@/utils/availabilityCache';

// Import existing utilities
import {
  getISTDateString,
  getMinBookingDate,
  getMaxBookingDate,
  validateBookingDate as validateBookingDateUtil
} from '@/utils/timezone';
import {
  sanitizeBookingInput,
  sanitizePhoneNumber,
  generateCSRFToken,
  validateBookingData,
  type SecureBookingData
} from '@/utils/bookingSecurity';

// Import Supabase client
import { supabase } from '@/integrations/supabase/client';

// Declare Razorpay global
declare global {
  interface Window {
    Razorpay: any;
  }
}

// Optimized interfaces
interface BookingSelection {
  venue: string;
  sport: string;
  court: string;
  date: string;
  slots: string[];
  prices: Record<string, number>;
}

interface Venue {
  id: string;
  name: string;
  location: string;
}

interface Sport {
  id: string;
  name: string;
  icon?: string;
}

interface Court {
  id: string;
  name: string;
  venue_id: string;
  sport_id: string;
  hourly_rate: number;
}

interface QuickSlot {
  display: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
  name: string;
  image_url?: string;
  location?: string;
  description?: string;
  rating?: number;
}

interface Sport {
  id: string;
  name: string;
  icon_name?: string;
  booking_type?: string;
}

interface Court {
  id: string;
  name: string;
  venue_id: string;
  sport_id: string;
  court_group_id: string | null;
  hourly_rate: number;
  description?: string;
}

interface QuickSlot {
  display: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
}

interface BookingPageProps {
  onBookingComplete?: () => void;
}

const BookingPage: React.FC<BookingPageProps> = ({ onBookingComplete }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // URL parameters
  const initialVenueId = searchParams.get('venue');
  const initialSportId = searchParams.get('sport');
  const initialCourtId = searchParams.get('court');
  const initialDate = searchParams.get('date');

  // Optimized hooks
  const { smartDefaults, loading: smartLoading } = useSmartBookingDefaults();
  const { getCachedAvailability, preloadAvailability, getCacheStats } = useAvailabilityCache();

  // State management - optimized for 2-step flow
  const [currentStep, setCurrentStep] = useState(1); // 1: Selection, 2: Payment
  const [bookingSelection, setBookingSelection] = useState<BookingSelection | null>(null);

  // Loading states
  const [loading, setLoading] = useState({
    payment: false,
    booking: false,
    venues: false,
    sports: false,
    courts: false,
    availability: false
  });

  // Guest details (for Step 2)
  const [guestDetails, setGuestDetails] = useState({
    name: '',
    phone: '',
    couponCode: ''
  });

  // UI state for consolidated interface
  const [venues, setVenues] = useState<Venue[]>([]);
  const [sports, setSports] = useState<Sport[]>([]);
  const [courts, setCourts] = useState<Court[]>([]);
  const [selectedVenue, setSelectedVenue] = useState(initialVenueId || '');
  const [selectedSport, setSelectedSport] = useState(initialSportId || '');
  const [selectedCourt, setSelectedCourt] = useState(initialCourtId || '');
  const [selectedDate, setSelectedDate] = useState(initialDate || getISTDateString());
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [selectedSlotPrices, setSelectedSlotPrices] = useState<Record<string, number>>({});
  const [quickSlots, setQuickSlots] = useState<QuickSlot[]>([]);

  // Mobile UI state
  const [activeSection, setActiveSection] = useState<'selection' | 'slots'>('selection');
  const [isSelectionPanelCollapsed, setIsSelectionPanelCollapsed] = useState(false);
  const [showSmartSuggestions, setShowSmartSuggestions] = useState(true);

  // Security and success state
  const [csrfToken] = useState(() => generateCSRFToken());
  const [authVerified, setAuthVerified] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [bookingReference, setBookingReference] = useState('');
  // Verify authentication on mount
  useEffect(() => {
    if (user) {
      setAuthVerified(true);
    } else {
      navigate('/login');
    }
  }, [user, navigate]);

  // Apply smart defaults when available
  useEffect(() => {
    if (smartDefaults && !smartLoading) {
      if (smartDefaults.venue && !selectedVenue) {
        setSelectedVenue(smartDefaults.venue);
      }
      if (smartDefaults.sport && !selectedSport) {
        setSelectedSport(smartDefaults.sport);
      }
      if (smartDefaults.court && !selectedCourt) {
        setSelectedCourt(smartDefaults.court);
      }
      if (smartDefaults.date && !selectedDate) {
        setSelectedDate(smartDefaults.date);
      }
    }
  }, [smartDefaults, smartLoading, selectedVenue, selectedSport, selectedCourt, selectedDate]);

  // Fetch venues
  const fetchVenues = useCallback(async () => {
    setLoading(prev => ({ ...prev, venues: true }));
    try {
      const { data, error } = await supabase
        .from('venues')
        .select('id, name, location')
        .eq('is_active', true);

      if (error) throw error;
      setVenues(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load venues",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, venues: false }));
    }
  }, [toast]);

  // Fetch sports for selected venue
  const fetchSports = useCallback(async (venueId: string) => {
    if (!venueId) return;

    setLoading(prev => ({ ...prev, sports: true }));
    try {
      const { data, error } = await supabase
        .from('venue_sports')
        .select('sport_id, sports(id, name)')
        .eq('venue_id', venueId);

      if (error) throw error;

      const sportsData = data?.map(item => ({
        id: item.sports.id,
        name: item.sports.name
      })) || [];

      setSports(sportsData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load sports",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, sports: false }));
    }
  }, [toast]);

  // Fetch courts for selected venue and sport
  const fetchCourts = useCallback(async (venueId: string, sportId: string) => {
    if (!venueId || !sportId) return;

    setLoading(prev => ({ ...prev, courts: true }));
    try {
      const { data, error } = await supabase
        .from('courts')
        .select('id, name, venue_id, sport_id, hourly_rate')
        .eq('venue_id', venueId)
        .eq('sport_id', sportId)
        .eq('is_active', true);

      if (error) throw error;
      setCourts(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load courts",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, courts: false }));
    }
  }, [toast]);

  // Optimized availability fetching with caching
  const fetchQuickSlots = useCallback(async () => {
    if (!selectedCourt || !selectedDate) return;

    setLoading(prev => ({ ...prev, availability: true }));
    try {
      const slots = await getCachedAvailability(selectedCourt, selectedDate);

      const quickSlotData: QuickSlot[] = slots.map(slot => ({
        display: `${slot.start_time} - ${slot.end_time}`,
        startTime: slot.start_time,
        endTime: slot.end_time,
        price: parseFloat(slot.price),
        isAvailable: slot.is_available
      }));

      setQuickSlots(quickSlotData);

      // Auto-switch to slots view if selections are complete and on mobile
      if (isMobile && selectedVenue && selectedSport && selectedCourt && selectedDate) {
        setActiveSection('slots');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load available slots",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, availability: false }));
    }
  }, [selectedCourt, selectedDate, getCachedAvailability, toast, selectedVenue, selectedSport, isMobile]);

  // Load initial data
  useEffect(() => {
    fetchVenues();
  }, [fetchVenues]);

  // Load sports when venue changes
  useEffect(() => {
    if (selectedVenue) {
      fetchSports(selectedVenue);
      setSelectedSport('');
      setSelectedCourt('');
    }
  }, [selectedVenue, fetchSports]);

  // Load courts when venue and sport change
  useEffect(() => {
    if (selectedVenue && selectedSport) {
      fetchCourts(selectedVenue, selectedSport);
      setSelectedCourt('');
    }
  }, [selectedVenue, selectedSport, fetchCourts]);

  // Fetch slots when court/date changes
  useEffect(() => {
    if (selectedCourt && selectedDate) {
      fetchQuickSlots();
    }
  }, [selectedCourt, selectedDate, fetchQuickSlots]);

  // Handle slot selection
  const handleSlotToggle = useCallback((slot: QuickSlot) => {
    if (!slot.isAvailable) return;

    const slotDisplay = slot.display;
    const isSelected = selectedSlots.includes(slotDisplay);

    if (isSelected) {
      // Remove slot
      setSelectedSlots(prev => prev.filter(s => s !== slotDisplay));
      setSelectedSlotPrices(prev => {
        const updated = { ...prev };
        delete updated[slotDisplay];
        return updated;
      });
    } else {
      // Add slot
      setSelectedSlots(prev => [...prev, slotDisplay]);
      setSelectedSlotPrices(prev => ({
        ...prev,
        [slotDisplay]: slot.price
      }));
    }
  }, [selectedSlots]);

  // Handle selection completion from Step 1
  const handleSelectionComplete = useCallback(() => {
    if (!selectedVenue || !selectedSport || !selectedCourt || !selectedDate || selectedSlots.length === 0) {
      toast({
        title: "Incomplete Selection",
        description: "Please complete all selections before proceeding",
        variant: "destructive",
      });
      return;
    }

    const selection: BookingSelection = {
      venue: selectedVenue,
      sport: selectedSport,
      court: selectedCourt,
      date: selectedDate,
      slots: selectedSlots,
      prices: selectedSlotPrices
    };

    setBookingSelection(selection);
    setCurrentStep(2);
  }, [selectedVenue, selectedSport, selectedCourt, selectedDate, selectedSlots, selectedSlotPrices, toast]);

  // Calculate total price
  const calculateTotalPrice = useCallback(() => {
    if (bookingSelection) {
      return Object.values(bookingSelection.prices).reduce((sum, price) => sum + price, 0);
    }
    return Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0);
  }, [bookingSelection, selectedSlotPrices]);
  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          resolve(true);
        };
        script.onerror = () => {
          resolve(false);
        };
        document.body.appendChild(script);
      });
    };

    loadRazorpayScript();
  }, []);



  // Initialize data on mount
  useEffect(() => {
    if (authVerified) {
      fetchVenues();
      fetchSports();
    }
  }, [authVerified, fetchVenues, fetchSports]);

  // Fetch courts when venue and sport are selected
  useEffect(() => {
    if (selectedVenue && selectedSport) {
      fetchCourts(selectedVenue, selectedSport);
    }
  }, [selectedVenue, selectedSport, fetchCourts]);

  // Handle payment processing (preserved from original BookingPage.tsx)
  const handlePayment = useCallback(async () => {
    if (!authVerified || !user || !bookingSelection) {
      toast({
        title: "Missing information",
        description: "Please complete all booking details",
        variant: "destructive",
      });
      return;
    }

    setLoading(prev => ({ ...prev, payment: true }));

    try {
      // Validate booking data
      const secureBookingData: SecureBookingData = {
        courtId: bookingSelection.court,
        date: bookingSelection.date,
        slots: bookingSelection.slots,
        guestName: sanitizeBookingInput(guestDetails.name),
        guestPhone: sanitizeBookingInput(guestDetails.phone),
        couponCode: guestDetails.couponCode,
        csrfToken: csrfToken
      };

      const validation = validateBookingData(secureBookingData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Create Razorpay order
      const totalAmount = calculateTotalPrice();
      const receipt = `booking_${Date.now()}_${user.id.substring(0, 8)}`;

      const response = await supabase.functions.invoke('create-razorpay-order', {
        body: {
          amount: totalAmount * 100, // Convert to paise
          receipt: receipt,
          notes: {
            court_id: bookingSelection.court,
            date: bookingSelection.date,
            slots: bookingSelection.slots.join(', '),
            venueId: bookingSelection.venue,
            sportId: bookingSelection.sport,
            user_id: user.id,
            guest_name: guestDetails.name,
            guest_phone: guestDetails.phone
          },
          courtId: bookingSelection.court,
          date: bookingSelection.date,
          couponCode: guestDetails.couponCode,
          guestName: guestDetails.name,
          guestPhone: guestDetails.phone,
          csrfToken: csrfToken
        }
      });

      if (response.error) {
        throw new Error('Payment order creation failed');
      }

      const { order, key_id } = response.data;

      // Initialize Razorpay
      const options = {
        key: key_id,
        amount: order.amount,
        currency: order.currency,
        name: "Grid२Play",
        description: `Court Booking for ${bookingSelection.date}`,
        order_id: order.id,
        prefill: {
          name: sanitizeBookingInput(guestDetails.name || user.email || ''),
          email: user.email,
          contact: sanitizeBookingInput(guestDetails.phone || '')
        },
        theme: {
          color: "#047857" // Emerald-800
        },
        handler: async (response: any) => {
          await handleBookingCreation(response.razorpay_payment_id, order.id, response.razorpay_signature);
        },
        modal: {
          ondismiss: () => {
            setLoading(prev => ({ ...prev, payment: false }));
          }
        }
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Payment initialization error:', error);
      toast({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to initialize payment",
        variant: "destructive",
      });
      setLoading(prev => ({ ...prev, payment: false }));
    }
  }, [authVerified, user, bookingSelection, guestDetails, csrfToken, calculateTotalPrice, toast]);

  // Handle booking creation after successful payment
  const handleBookingCreation = useCallback(async (
    paymentId: string,
    orderId: string,
    signature: string
  ) => {
    if (!bookingSelection || !user) return;

    setLoading(prev => ({ ...prev, booking: true }));

    try {
      // Create booking using existing function
      const startTime = bookingSelection.slots[0].split(' - ')[0];
      const endTime = bookingSelection.slots[bookingSelection.slots.length - 1].split(' - ')[1];
      const originalPrice = calculateTotalPrice();

      const { data, error } = await supabase.rpc('create_booking_with_coupon', {
        p_court_id: bookingSelection.court,
        p_user_id: user.id,
        p_booking_date: bookingSelection.date,
        p_start_time: startTime,
        p_end_time: endTime,
        p_original_price: originalPrice,
        p_coupon_code: guestDetails.couponCode || null,
        p_guest_name: guestDetails.name || null,
        p_guest_phone: guestDetails.phone || null,
        p_payment_reference: paymentId,
        p_payment_status: 'completed',
        p_payment_method: 'online'
      });

      if (error) throw error;

      // Success
      setBookingReference(data.booking_reference);
      setShowSuccessModal(true);

      toast({
        title: "Booking Confirmed!",
        description: `Your booking has been confirmed. Reference: ${data.booking_reference}`,
      });

      // Call onBookingComplete if provided
      if (onBookingComplete) {
        onBookingComplete();
      }

    } catch (error) {
      console.error('Booking creation error:', error);
      toast({
        title: "Booking Failed",
        description: error instanceof Error ? error.message : "Failed to create booking",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, booking: false, payment: false }));
    }
  }, [bookingSelection, user, guestDetails, calculateTotalPrice, toast, onBookingComplete]);

  // Step animations
  const stepVariants = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  };

  // Smart suggestion component
  const SmartSuggestion = ({ type, value, reason, confidence }: {
    type: string;
    value: string;
    reason: string;
    confidence: number;
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-3 mb-3"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Star className="w-4 h-4 text-emerald-400" />
          <span className="text-sm font-medium text-emerald-300">Smart Pick</span>
          <div className="flex">
            {[...Array(Math.ceil(confidence * 5))].map((_, i) => (
              <Star key={i} className="w-3 h-3 text-emerald-400 fill-current" />
            ))}
          </div>
        </div>
        <button
          onClick={() => setShowSmartSuggestions(false)}
          className="text-gray-400 hover:text-white transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      <p className="text-xs text-gray-300 mt-1">{reason}</p>
    </motion.div>
  );

  if (!authVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Loader className="w-8 h-8 animate-spin text-emerald-400" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <button
              onClick={() => currentStep === 1 ? navigate(-1) : setCurrentStep(1)}
              className="p-2 hover:bg-gray-800 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
            >
              <ArrowLeft className="w-5 h-5 text-white" />
            </button>
            <h1 className="text-lg font-bold text-white">
              {currentStep === 1 ? 'Quick Booking' : 'Confirm & Pay'}
            </h1>
          </div>

          {/* Step indicator */}
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= 1 ? 'bg-emerald-600 text-white' : 'bg-gray-700 text-gray-400'
            }`}>
              {currentStep > 1 ? <Check size={16} /> : '1'}
            </div>
            <div className={`w-8 h-1 ${currentStep >= 2 ? 'bg-emerald-600' : 'bg-gray-700'}`} />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= 2 ? 'bg-emerald-600 text-white' : 'bg-gray-700 text-gray-400'
            }`}>
              2
            </div>
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {(loading.booking || loading.payment) && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-gray-900 p-6 rounded-xl border border-emerald-500/20 text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto mb-4"
            />
            <p className="text-white font-medium">
              {loading.payment ? 'Initializing Payment...' : 'Processing Booking...'}
            </p>
          </div>
        </div>
      )}

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentStep === 1 && (
          <motion.div
            key="step1"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {isMobile ? (
              // Mobile Consolidated Interface
              <div className="min-h-screen bg-gray-900">
                {/* Mobile Section Toggle */}
                <div className="flex bg-gray-800 mx-4 mb-4 rounded-lg p-1">
                  <button
                    onClick={() => setActiveSection('selection')}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all min-h-[44px] ${
                      activeSection === 'selection'
                        ? 'bg-emerald-600 text-white'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Selection
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveSection('slots')}
                    disabled={!selectedCourt || !selectedDate}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all min-h-[44px] ${
                      activeSection === 'slots'
                        ? 'bg-emerald-600 text-white'
                        : 'text-gray-400 hover:text-white disabled:opacity-50'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <Clock className="w-4 h-4" />
                      Slots ({quickSlots.filter(s => s.isAvailable).length})
                    </div>
                  </button>
                </div>

                {/* Mobile Content */}
                <div className="px-4 pb-24">
                  <AnimatePresence mode="wait">
                    {activeSection === 'selection' && (
                      <motion.div
                        key="selection"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        className="space-y-4"
                      >
                        {/* Smart Suggestions */}
                        {showSmartSuggestions && smartDefaults?.venue && smartDefaults?.confidence?.venue > 0.5 && (
                          <SmartSuggestion
                            type="venue"
                            value={smartDefaults.venue}
                            reason={smartDefaults?.reasons?.venue || ''}
                            confidence={smartDefaults.confidence.venue}
                          />
                        )}

                        {/* Selection Cards */}
                        <div className="space-y-3">
                          {/* Venue Selection */}
                          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex items-center gap-3 mb-3">
                              <MapPin className="w-5 h-5 text-emerald-400" />
                              <span className="font-medium text-white">Venue</span>
                            </div>
                            <select
                              value={selectedVenue}
                              onChange={(e) => setSelectedVenue(e.target.value)}
                              className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                              disabled={loading.venues}
                            >
                              <option value="">Select a venue</option>
                              {venues.map(venue => (
                                <option key={venue.id} value={venue.id}>{venue.name}</option>
                              ))}
                            </select>
                          </div>

                          {/* Sport Selection */}
                          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex items-center gap-3 mb-3">
                              <Activity className="w-5 h-5 text-emerald-400" />
                              <span className="font-medium text-white">Sport</span>
                            </div>
                            <select
                              value={selectedSport}
                              onChange={(e) => setSelectedSport(e.target.value)}
                              className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                              disabled={!selectedVenue || loading.sports}
                            >
                              <option value="">Select a sport</option>
                              {sports.map(sport => (
                                <option key={sport.id} value={sport.id}>{sport.name}</option>
                              ))}
                            </select>
                          </div>

                          {/* Court Selection */}
                          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex items-center gap-3 mb-3">
                              <MapPin className="w-5 h-5 text-emerald-400" />
                              <span className="font-medium text-white">Court</span>
                            </div>
                            <select
                              value={selectedCourt}
                              onChange={(e) => setSelectedCourt(e.target.value)}
                              className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                              disabled={!selectedSport || loading.courts}
                            >
                              <option value="">Select a court</option>
                              {courts.map(court => (
                                <option key={court.id} value={court.id}>{court.name}</option>
                              ))}
                            </select>
                          </div>

                          {/* Date Selection */}
                          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex items-center gap-3 mb-3">
                              <Calendar className="w-5 h-5 text-emerald-400" />
                              <span className="font-medium text-white">Date</span>
                            </div>
                            <input
                              type="date"
                              value={selectedDate}
                              onChange={(e) => setSelectedDate(e.target.value)}
                              min={getMinBookingDate()}
                              max={getMaxBookingDate()}
                              className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                            />
                          </div>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {activeSection === 'slots' && (
                      <motion.div
                        key="slots"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        className="space-y-4"
                      >
                        {/* Quick Slots Grid */}
                        <div className="grid grid-cols-2 gap-3">
                          {quickSlots.map((slot, index) => {
                            const isSelected = selectedSlots.includes(slot.display);
                            const isAvailable = slot.isAvailable;

                            return (
                              <motion.button
                                key={slot.display}
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: index * 0.05 }}
                                onClick={() => handleSlotToggle(slot)}
                                disabled={!isAvailable}
                                className={`
                                  p-4 rounded-xl border-2 transition-all min-h-[80px] touch-manipulation
                                  ${isSelected
                                    ? 'bg-emerald-600 border-emerald-500 text-white'
                                    : isAvailable
                                    ? 'bg-gray-800 border-gray-600 text-white hover:border-emerald-500'
                                    : 'bg-gray-800/50 border-gray-700 text-gray-500 cursor-not-allowed'
                                  }
                                `}
                              >
                                <div className="flex flex-col items-center gap-1">
                                  <div className="text-sm font-medium">
                                    {slot.startTime} - {slot.endTime}
                                  </div>
                                  <div className="text-lg font-bold">
                                    ₹{slot.price}
                                  </div>
                                  {isSelected && (
                                    <Check className="w-4 h-4 text-white" />
                                  )}
                                  {!isAvailable && (
                                    <X className="w-4 h-4 text-gray-500" />
                                  )}
                                </div>
                              </motion.button>
                            );
                          })}
                        </div>

                        {quickSlots.length === 0 && !loading.availability && (
                          <div className="text-center py-12 text-gray-400">
                            <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                            <p>No slots available for selected date</p>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Fixed Bottom Bar */}
                <div className="fixed bottom-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-t border-gray-800 p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="text-white">
                      <div className="text-sm text-gray-400">Total</div>
                      <div className="text-2xl font-bold text-emerald-400">₹{calculateTotalPrice()}</div>
                    </div>
                    <div className="text-right text-sm text-gray-400">
                      {selectedSlots.length} slot{selectedSlots.length !== 1 ? 's' : ''} selected
                    </div>
                  </div>

                  <button
                    onClick={handleSelectionComplete}
                    disabled={selectedSlots.length === 0 || loading.availability}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white min-h-[48px] text-lg font-semibold rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading.availability ? (
                      <Loader className="w-5 h-5 animate-spin mx-auto" />
                    ) : (
                      `Continue to Payment - ₹${calculateTotalPrice()}`
                    )}
                  </button>
                </div>
              </div>
            ) : (
              // Desktop Interface
              <div className="container mx-auto px-4 py-6 max-w-6xl">
                <div className="grid grid-cols-2 gap-8">
                  {/* Left Panel - Selection */}
                  <div className="space-y-6">
                    <h2 className="text-xl font-bold text-emerald-400 mb-4">Make Your Selection</h2>

                    {/* Venue Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                        <MapPin size={16} className="text-emerald-400" />
                        Select Venue
                      </label>
                      <select
                        value={selectedVenue}
                        onChange={(e) => setSelectedVenue(e.target.value)}
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white"
                        disabled={loading.venues}
                      >
                        <option value="">Select a venue</option>
                        {venues.map(venue => (
                          <option key={venue.id} value={venue.id}>{venue.name}</option>
                        ))}
                      </select>
                    </div>

                    {/* Sport Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                        <Activity size={16} className="text-emerald-400" />
                        Select Sport
                      </label>
                      <select
                        value={selectedSport}
                        onChange={(e) => setSelectedSport(e.target.value)}
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white"
                        disabled={!selectedVenue || loading.sports}
                      >
                        <option value="">Select a sport</option>
                        {sports.map(sport => (
                          <option key={sport.id} value={sport.id}>{sport.name}</option>
                        ))}
                      </select>
                    </div>

                    {/* Court Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300">Select Court</label>
                      <select
                        value={selectedCourt}
                        onChange={(e) => setSelectedCourt(e.target.value)}
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white"
                        disabled={!selectedSport || loading.courts}
                      >
                        <option value="">Select a court</option>
                        {courts.map(court => (
                          <option key={court.id} value={court.id}>{court.name}</option>
                        ))}
                      </select>
                    </div>

                    {/* Date Selection */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                        <Calendar size={16} className="text-emerald-400" />
                        Select Date
                      </label>
                      <input
                        type="date"
                        value={selectedDate}
                        onChange={(e) => setSelectedDate(e.target.value)}
                        min={getMinBookingDate()}
                        max={getMaxBookingDate()}
                        className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white"
                      />
                    </div>
                  </div>

                  {/* Right Panel - Slots */}
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-bold text-emerald-400">Available Slots</h2>
                      {loading.availability && (
                        <Loader className="animate-spin" size={16} />
                      )}
                    </div>

                    {quickSlots.length === 0 ? (
                      <div className="text-center py-12 text-gray-400">
                        {selectedCourt && selectedDate ? (
                          loading.availability ? "Loading slots..." : "No slots available"
                        ) : (
                          "Select court and date to view slots"
                        )}
                      </div>
                    ) : (
                      <div className="grid grid-cols-3 gap-3">
                        {quickSlots.map((slot, index) => {
                          const isSelected = selectedSlots.includes(slot.display);
                          const isAvailable = slot.isAvailable;

                          return (
                            <button
                              key={slot.display}
                              onClick={() => handleSlotToggle(slot)}
                              disabled={!isAvailable}
                              className={`
                                p-3 rounded-lg border text-sm font-medium transition-all
                                ${isSelected
                                  ? 'bg-emerald-600 border-emerald-500 text-white'
                                  : isAvailable
                                  ? 'bg-gray-800 border-gray-600 text-white hover:border-emerald-500'
                                  : 'bg-gray-900 border-gray-700 text-gray-500 cursor-not-allowed'
                                }
                              `}
                            >
                              <div>{slot.startTime} - {slot.endTime}</div>
                              <div className="text-xs opacity-75">₹{slot.price}</div>
                            </button>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        )}

        {currentStep === 2 && bookingSelection && (
          <motion.div
            key="step2"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="container mx-auto px-4 py-6 max-w-2xl"
          >
            {/* Step 2: Payment confirmation - preserved from original */}
            <div className="space-y-6">
              {/* Booking Summary */}
              <div className="bg-gray-800 rounded-xl p-6 border border-emerald-800/30">
                <h3 className="text-lg font-semibold text-white mb-4">Booking Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Date:</span>
                    <span className="text-white">{bookingSelection.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Slots:</span>
                    <span className="text-white">{bookingSelection.slots.join(', ')}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg pt-2 border-t border-gray-700">
                    <span className="text-gray-300">Total:</span>
                    <span className="text-emerald-400">₹{calculateTotalPrice()}</span>
                  </div>
                </div>
              </div>

              {/* Guest Details Form */}
              <div className="bg-gray-800 rounded-xl p-6 border border-emerald-800/30">
                <h3 className="text-lg font-semibold text-white mb-4">Guest Details</h3>
                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Full Name"
                    value={guestDetails.name}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                  <input
                    type="tel"
                    placeholder="Phone Number"
                    value={guestDetails.phone}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                  <input
                    type="text"
                    placeholder="Coupon Code (Optional)"
                    value={guestDetails.couponCode}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, couponCode: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                </div>
              </div>

              {/* Payment Button */}
              <button
                onClick={handlePayment}
                disabled={loading.booking || loading.payment}
                className="w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-600 text-white py-4 text-lg font-semibold rounded-lg transition-all min-h-[48px] flex items-center justify-center gap-2"
              >
                {loading.payment || loading.booking ? (
                  <Loader className="animate-spin" size={20} />
                ) : (
                  <>
                    <CreditCard size={20} />
                    Pay ₹{calculateTotalPrice()} & Confirm Booking
                  </>
                )}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success Modal */}
      <BookingSuccessModal
        isOpen={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          navigate('/profile');
        }}
        bookingDetails={{
          booking_reference: bookingReference,
          guest_name: guestDetails.name || user?.email?.split('@')[0] || 'Guest',
          guest_phone: guestDetails.phone || '',
          venue_name: venues.find(v => v.id === selectedVenue)?.name || 'Venue',
          venue_location: venues.find(v => v.id === selectedVenue)?.location || 'Location',
          court_name: courts.find(c => c.id === selectedCourt)?.name || 'Court',
          sport_name: sports.find(s => s.id === selectedSport)?.name || 'Sport',
          booking_date: selectedDate,
          start_time: selectedSlots[0]?.split(' - ')[0] || '',
          end_time: selectedSlots[selectedSlots.length - 1]?.split(' - ')[1] || '',
          total_price: calculateTotalPrice().toString()
        }}
      />
    </div>
  );
};

export default BookingPage;
