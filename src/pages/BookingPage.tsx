import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Check, CreditCard, Loader, MapPin, Activity, Calendar, Clock, ChevronDown, ChevronUp, Zap, Star, RefreshCw, X } from 'lucide-react';

// Import optimized components and hooks
import BookingSuccessModal from '@/components/BookingSuccessModal';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSmartBookingDefaults } from '@/hooks/useSmartBookingDefaults';
import { useAvailabilityCache } from '@/utils/availabilityCache';

// Import existing utilities
import {
  getISTDateString,
  getMinBookingDate,
  getMaxBookingDate,
  validateBookingDate as validateBookingDateUtil
} from '@/utils/timezone';
import {
  sanitizeBookingInput,
  sanitizePhoneNumber,
  generateCSRFToken,
  validateBookingData,
  type SecureBookingData
} from '@/utils/bookingSecurity';

// Import Supabase client
import { supabase } from '@/integrations/supabase/client';

// Declare Razorpay global
declare global {
  interface Window {
    Razorpay: any;
  }
}

// Optimized interfaces
interface BookingSelection {
  venue: string;
  sport: string;
  court: string;
  date: string;
  slots: string[];
  prices: Record<string, number>;
}

interface Venue {
  id: string;
  name: string;
  image_url?: string;
  location?: string;
  description?: string;
  rating?: number;
}

interface Sport {
  id: string;
  name: string;
  icon_name?: string;
  booking_type?: string;
}

interface Court {
  id: string;
  name: string;
  venue_id: string;
  sport_id: string;
  court_group_id: string | null;
  hourly_rate: number;
  description?: string;
}

interface QuickSlot {
  display: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
}

interface BookingPageProps {
  onBookingComplete?: () => void;
}

const BookingPage: React.FC<BookingPageProps> = ({ onBookingComplete }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // URL parameters
  const initialVenueId = searchParams.get('venue');
  const initialSportId = searchParams.get('sport');
  const initialCourtId = searchParams.get('court');
  const initialDate = searchParams.get('date');

  // Optimized hooks
  const { smartDefaults, loading: smartLoading } = useSmartBookingDefaults();
  const { getCachedAvailability, preloadAvailability, getCacheStats } = useAvailabilityCache();

  // State management - optimized for 2-step flow
  const [currentStep, setCurrentStep] = useState(1); // 1: Selection, 2: Payment
  const [bookingSelection, setBookingSelection] = useState<BookingSelection | null>(null);

  // Loading states
  const [loading, setLoading] = useState({
    payment: false,
    booking: false,
    venues: false,
    sports: false,
    courts: false,
    availability: false
  });

  // Guest details (for Step 2)
  const [guestDetails, setGuestDetails] = useState({
    name: '',
    phone: '',
    couponCode: ''
  });

  // UI state for consolidated interface
  const [venues, setVenues] = useState<Venue[]>([]);
  const [sports, setSports] = useState<Sport[]>([]);
  const [courts, setCourts] = useState<Court[]>([]);
  const [selectedVenue, setSelectedVenue] = useState(initialVenueId || '');
  const [selectedSport, setSelectedSport] = useState(initialSportId || '');
  const [selectedCourt, setSelectedCourt] = useState(initialCourtId || '');
  const [selectedDate, setSelectedDate] = useState(initialDate || getISTDateString());
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [selectedSlotPrices, setSelectedSlotPrices] = useState<Record<string, number>>({});
  const [quickSlots, setQuickSlots] = useState<QuickSlot[]>([]);

  // Mobile UI state
  const [activeSection, setActiveSection] = useState<'selection' | 'slots'>('selection');
  const [isSelectionPanelCollapsed, setIsSelectionPanelCollapsed] = useState(false);
  const [showSmartSuggestions, setShowSmartSuggestions] = useState(true);

  // Security and success state
  const [csrfToken] = useState(() => generateCSRFToken());
  const [authVerified, setAuthVerified] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [bookingReference, setBookingReference] = useState('');
  // Verify authentication on mount
  useEffect(() => {
    if (user) {
      setAuthVerified(true);
    } else {
      navigate('/login');
    }
  }, [user, navigate]);

  // Apply smart defaults when available
  useEffect(() => {
    if (smartDefaults && !smartLoading) {
      if (smartDefaults.venue && !selectedVenue) {
        setSelectedVenue(smartDefaults.venue);
      }
      if (smartDefaults.sport && !selectedSport) {
        setSelectedSport(smartDefaults.sport);
      }
      if (smartDefaults.court && !selectedCourt) {
        setSelectedCourt(smartDefaults.court);
      }
      if (smartDefaults.date && !selectedDate) {
        setSelectedDate(smartDefaults.date);
      }
    }
  }, [smartDefaults, smartLoading, selectedVenue, selectedSport, selectedCourt, selectedDate]);

  // Optimized availability fetching with caching
  const fetchQuickSlots = useCallback(async () => {
    if (!selectedCourt || !selectedDate) return;

    setLoading(prev => ({ ...prev, availability: true }));
    try {
      const slots = await getCachedAvailability(selectedCourt, selectedDate);

      const quickSlotData: QuickSlot[] = slots.map(slot => ({
        display: `${slot.start_time} - ${slot.end_time}`,
        startTime: slot.start_time,
        endTime: slot.end_time,
        price: parseFloat(slot.price),
        isAvailable: slot.is_available
      }));

      setQuickSlots(quickSlotData);

      // Auto-switch to slots view if selections are complete and on mobile
      if (isMobile && selectedVenue && selectedSport && selectedCourt && selectedDate) {
        setActiveSection('slots');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load available slots",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, availability: false }));
    }
  }, [selectedCourt, selectedDate, getCachedAvailability, toast, selectedVenue, selectedSport, isMobile]);

  // Fetch slots when court/date changes
  useEffect(() => {
    fetchQuickSlots();
  }, [fetchQuickSlots]);

  // Handle slot selection
  const handleSlotToggle = useCallback((slot: QuickSlot) => {
    if (!slot.isAvailable) return;

    const slotDisplay = slot.display;
    const isSelected = selectedSlots.includes(slotDisplay);

    if (isSelected) {
      // Remove slot
      setSelectedSlots(prev => prev.filter(s => s !== slotDisplay));
      setSelectedSlotPrices(prev => {
        const updated = { ...prev };
        delete updated[slotDisplay];
        return updated;
      });
    } else {
      // Add slot
      setSelectedSlots(prev => [...prev, slotDisplay]);
      setSelectedSlotPrices(prev => ({
        ...prev,
        [slotDisplay]: slot.price
      }));
    }
  }, [selectedSlots]);

  // Handle selection completion from Step 1
  const handleSelectionComplete = useCallback(() => {
    if (!selectedVenue || !selectedSport || !selectedCourt || !selectedDate || selectedSlots.length === 0) {
      toast({
        title: "Incomplete Selection",
        description: "Please complete all selections before proceeding",
        variant: "destructive",
      });
      return;
    }

    const selection: BookingSelection = {
      venue: selectedVenue,
      sport: selectedSport,
      court: selectedCourt,
      date: selectedDate,
      slots: selectedSlots,
      prices: selectedSlotPrices
    };

    setBookingSelection(selection);
    setCurrentStep(2);
  }, [selectedVenue, selectedSport, selectedCourt, selectedDate, selectedSlots, selectedSlotPrices, toast]);

  // Calculate total price
  const calculateTotalPrice = useCallback(() => {
    if (bookingSelection) {
      return Object.values(bookingSelection.prices).reduce((sum, price) => sum + price, 0);
    }
    return Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0);
  }, [bookingSelection, selectedSlotPrices]);
  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          resolve(true);
        };
        script.onerror = () => {
          resolve(false);
        };
        document.body.appendChild(script);
      });
    };

    loadRazorpayScript();
  }, []);

  // Optimized data fetching functions
  const fetchVenues = useCallback(async () => {
    setLoading(prev => ({ ...prev, venues: true }));
    try {
      const { data, error } = await supabase
        .from('venues')
        .select('id, name, image_url, location, description, rating')
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) throw error;
      setVenues(data || []);
    } catch (error) {
      console.error('Error fetching venues:', error);
      toast({
        title: "Error",
        description: "Failed to load venues",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, venues: false }));
    }
  }, [toast]);

  const fetchSports = useCallback(async () => {
    setLoading(prev => ({ ...prev, sports: true }));
    try {
      const { data, error } = await supabase
        .from('sports')
        .select('id, name, icon_name, booking_type')
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) throw error;
      setSports(data || []);
    } catch (error) {
      console.error('Error fetching sports:', error);
      toast({
        title: "Error",
        description: "Failed to load sports",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, sports: false }));
    }
  }, [toast]);

  const fetchCourts = useCallback(async (venueId: string, sportId: string) => {
    if (!venueId || !sportId) return;

    setLoading(prev => ({ ...prev, courts: true }));
    try {
      const { data, error } = await supabase
        .from('courts')
        .select('id, name, venue_id, sport_id, court_group_id, hourly_rate, description')
        .eq('venue_id', venueId)
        .eq('sport_id', sportId)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) throw error;
      setCourts(data || []);
    } catch (error) {
      console.error('Error fetching courts:', error);
      toast({
        title: "Error",
        description: "Failed to load courts",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, courts: false }));
    }
  }, [toast]);

  // Initialize data on mount
  useEffect(() => {
    if (authVerified) {
      fetchVenues();
      fetchSports();
    }
  }, [authVerified, fetchVenues, fetchSports]);

  // Fetch courts when venue and sport are selected
  useEffect(() => {
    if (selectedVenue && selectedSport) {
      fetchCourts(selectedVenue, selectedSport);
    }
  }, [selectedVenue, selectedSport, fetchCourts]);

  // Handle payment processing (preserved from original BookingPage.tsx)
  const handlePayment = useCallback(async () => {
    if (!authVerified || !user || !bookingSelection) {
      toast({
        title: "Missing information",
        description: "Please complete all booking details",
        variant: "destructive",
      });
      return;
    }

    setLoading(prev => ({ ...prev, payment: true }));

    try {
      // Validate booking data
      const secureBookingData: SecureBookingData = {
        courtId: bookingSelection.court,
        date: bookingSelection.date,
        slots: bookingSelection.slots,
        guestName: sanitizeBookingInput(guestDetails.name),
        guestPhone: sanitizeBookingInput(guestDetails.phone),
        couponCode: guestDetails.couponCode,
        csrfToken: csrfToken
      };

      const validation = validateBookingData(secureBookingData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Create Razorpay order
      const totalAmount = calculateTotalPrice();
      const receipt = `booking_${Date.now()}_${user.id.substring(0, 8)}`;

      const response = await supabase.functions.invoke('create-razorpay-order', {
        body: {
          amount: totalAmount * 100, // Convert to paise
          receipt: receipt,
          notes: {
            court_id: bookingSelection.court,
            date: bookingSelection.date,
            slots: bookingSelection.slots.join(', '),
            venueId: bookingSelection.venue,
            sportId: bookingSelection.sport,
            user_id: user.id,
            guest_name: guestDetails.name,
            guest_phone: guestDetails.phone
          },
          courtId: bookingSelection.court,
          date: bookingSelection.date,
          couponCode: guestDetails.couponCode,
          guestName: guestDetails.name,
          guestPhone: guestDetails.phone,
          csrfToken: csrfToken
        }
      });

      if (response.error) {
        throw new Error('Payment order creation failed');
      }

      const { order, key_id } = response.data;

      // Initialize Razorpay
      const options = {
        key: key_id,
        amount: order.amount,
        currency: order.currency,
        name: "Grid२Play",
        description: `Court Booking for ${bookingSelection.date}`,
        order_id: order.id,
        prefill: {
          name: sanitizeBookingInput(guestDetails.name || user.email || ''),
          email: user.email,
          contact: sanitizeBookingInput(guestDetails.phone || '')
        },
        theme: {
          color: "#047857" // Emerald-800
        },
        handler: async (response: any) => {
          await handleBookingCreation(response.razorpay_payment_id, order.id, response.razorpay_signature);
        },
        modal: {
          ondismiss: () => {
            setLoading(prev => ({ ...prev, payment: false }));
          }
        }
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Payment initialization error:', error);
      toast({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to initialize payment",
        variant: "destructive",
      });
      setLoading(prev => ({ ...prev, payment: false }));
    }
  }, [authVerified, user, bookingSelection, guestDetails, csrfToken, calculateTotalPrice, toast]);

  // Handle booking creation after successful payment
  const handleBookingCreation = useCallback(async (
    paymentId: string,
    orderId: string,
    signature: string
  ) => {
    if (!bookingSelection || !user) return;

    setLoading(prev => ({ ...prev, booking: true }));

    try {
      // Create booking using existing function
      const startTime = bookingSelection.slots[0].split(' - ')[0];
      const endTime = bookingSelection.slots[bookingSelection.slots.length - 1].split(' - ')[1];
      const originalPrice = calculateTotalPrice();

      const { data, error } = await supabase.rpc('create_booking_with_coupon', {
        p_court_id: bookingSelection.court,
        p_user_id: user.id,
        p_booking_date: bookingSelection.date,
        p_start_time: startTime,
        p_end_time: endTime,
        p_original_price: originalPrice,
        p_coupon_code: guestDetails.couponCode || null,
        p_guest_name: guestDetails.name || null,
        p_guest_phone: guestDetails.phone || null,
        p_payment_reference: paymentId,
        p_payment_status: 'completed',
        p_payment_method: 'online'
      });

      if (error) throw error;

      // Success
      setBookingReference(data.booking_reference);
      setShowSuccessModal(true);

      toast({
        title: "Booking Confirmed!",
        description: `Your booking has been confirmed. Reference: ${data.booking_reference}`,
      });

      // Call onBookingComplete if provided
      if (onBookingComplete) {
        onBookingComplete();
      }

    } catch (error) {
      console.error('Booking creation error:', error);
      toast({
        title: "Booking Failed",
        description: error instanceof Error ? error.message : "Failed to create booking",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, booking: false, payment: false }));
    }
  }, [bookingSelection, user, guestDetails, calculateTotalPrice, toast, onBookingComplete]);

  // Step animations
  const stepVariants = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  };

  // Smart suggestion component
  const SmartSuggestion = ({ type, value, reason, confidence }: {
    type: string;
    value: string;
    reason: string;
    confidence: number;
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-3 mb-3"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Star className="w-4 h-4 text-emerald-400" />
          <span className="text-sm font-medium text-emerald-300">Smart Pick</span>
          <div className="flex">
            {[...Array(Math.ceil(confidence * 5))].map((_, i) => (
              <Star key={i} className="w-3 h-3 text-emerald-400 fill-current" />
            ))}
          </div>
        </div>
        <button
          onClick={() => setShowSmartSuggestions(false)}
          className="text-gray-400 hover:text-white transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      <p className="text-xs text-gray-300 mt-1">{reason}</p>
    </motion.div>
  );

  if (!authVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Loader className="w-8 h-8 animate-spin text-emerald-400" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <button
              onClick={() => currentStep === 1 ? navigate(-1) : setCurrentStep(1)}
              className="p-2 hover:bg-gray-800 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
            >
              <ArrowLeft className="w-5 h-5 text-white" />
            </button>
            <h1 className="text-lg font-bold text-white">
              {currentStep === 1 ? 'Quick Booking' : 'Confirm & Pay'}
            </h1>
          </div>

          {/* Step indicator */}
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= 1 ? 'bg-emerald-600 text-white' : 'bg-gray-700 text-gray-400'
            }`}>
              {currentStep > 1 ? <Check size={16} /> : '1'}
            </div>
            <div className={`w-8 h-1 ${currentStep >= 2 ? 'bg-emerald-600' : 'bg-gray-700'}`} />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= 2 ? 'bg-emerald-600 text-white' : 'bg-gray-700 text-gray-400'
            }`}>
              2
            </div>
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {(loading.booking || loading.payment) && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-gray-900 p-6 rounded-xl border border-emerald-500/20 text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto mb-4"
            />
            <p className="text-white font-medium">
              {loading.payment ? 'Initializing Payment...' : 'Processing Booking...'}
            </p>
          </div>
        </div>
      )}

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentStep === 1 && (
          <motion.div
            key="step1"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {isMobile ? (
              // Mobile Consolidated Interface
              <div className="min-h-screen bg-gray-900">
                {/* Mobile Section Toggle */}
                <div className="flex bg-gray-800 mx-4 mb-4 rounded-lg p-1">
                  <button
                    onClick={() => setActiveSection('selection')}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all min-h-[44px] ${
                      activeSection === 'selection'
                        ? 'bg-emerald-600 text-white'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Selection
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveSection('slots')}
                    disabled={!selectedCourt || !selectedDate}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all min-h-[44px] ${
                      activeSection === 'slots'
                        ? 'bg-emerald-600 text-white'
                        : 'text-gray-400 hover:text-white disabled:opacity-50'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <Clock className="w-4 h-4" />
                      Slots ({quickSlots.filter(s => s.isAvailable).length})
                    </div>
                  </button>
                </div>

                {/* Mobile Content */}
                <div className="px-4 pb-24">
                  <AnimatePresence mode="wait">
                    {activeSection === 'selection' && (
                      <motion.div
                        key="selection"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        className="space-y-4"
                      >
                        {/* Smart Suggestions */}
                        {showSmartSuggestions && smartDefaults.venue && smartDefaults.confidence.venue > 0.5 && (
                          <SmartSuggestion
                            type="venue"
                            value={smartDefaults.venue}
                            reason={smartDefaults.reasons.venue || ''}
                            confidence={smartDefaults.confidence.venue}
                          />
                        )}

                        {/* Selection Cards - Placeholder for now */}
                        <div className="space-y-3">
                          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex items-center gap-3 mb-3">
                              <MapPin className="w-5 h-5 text-emerald-400" />
                              <span className="font-medium text-white">Venue</span>
                            </div>
                            <div className="text-sm text-gray-400">
                              Venue selection will be implemented
                            </div>
                          </div>

                          <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex items-center gap-3 mb-3">
                              <Activity className="w-5 h-5 text-emerald-400" />
                              <span className="font-medium text-white">Sport</span>
                            </div>
                            <div className="text-sm text-gray-400">
                              Sport selection will be implemented
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {activeSection === 'slots' && (
                      <motion.div
                        key="slots"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        className="space-y-4"
                      >
                        {/* Quick Slots Grid */}
                        <div className="grid grid-cols-2 gap-3">
                          {quickSlots.map((slot, index) => {
                            const isSelected = selectedSlots.includes(slot.display);
                            const isAvailable = slot.isAvailable;

                            return (
                              <motion.button
                                key={slot.display}
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: index * 0.05 }}
                                onClick={() => handleSlotToggle(slot)}
                                disabled={!isAvailable}
                                className={`
                                  p-4 rounded-xl border-2 transition-all min-h-[80px] touch-manipulation
                                  ${isSelected
                                    ? 'bg-emerald-600 border-emerald-500 text-white'
                                    : isAvailable
                                    ? 'bg-gray-800 border-gray-600 text-white hover:border-emerald-500'
                                    : 'bg-gray-800/50 border-gray-700 text-gray-500 cursor-not-allowed'
                                  }
                                `}
                              >
                                <div className="flex flex-col items-center gap-1">
                                  <div className="text-sm font-medium">
                                    {slot.startTime} - {slot.endTime}
                                  </div>
                                  <div className="text-lg font-bold">
                                    ₹{slot.price}
                                  </div>
                                  {isSelected && (
                                    <Check className="w-4 h-4 text-white" />
                                  )}
                                  {!isAvailable && (
                                    <X className="w-4 h-4 text-gray-500" />
                                  )}
                                </div>
                              </motion.button>
                            );
                          })}
                        </div>

                        {quickSlots.length === 0 && !loading.availability && (
                          <div className="text-center py-12 text-gray-400">
                            <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                            <p>No slots available for selected date</p>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Fixed Bottom Bar */}
                <div className="fixed bottom-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-t border-gray-800 p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="text-white">
                      <div className="text-sm text-gray-400">Total</div>
                      <div className="text-2xl font-bold text-emerald-400">₹{calculateTotalPrice()}</div>
                    </div>
                    <div className="text-right text-sm text-gray-400">
                      {selectedSlots.length} slot{selectedSlots.length !== 1 ? 's' : ''} selected
                    </div>
                  </div>

                  <button
                    onClick={handleSelectionComplete}
                    disabled={selectedSlots.length === 0 || loading.availability}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white min-h-[48px] text-lg font-semibold rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading.availability ? (
                      <Loader className="w-5 h-5 animate-spin mx-auto" />
                    ) : (
                      `Continue to Payment - ₹${calculateTotalPrice()}`
                    )}
                  </button>
                </div>
              </div>
            ) : (
              // Desktop Interface - Placeholder
              <div className="container mx-auto px-4 py-6 max-w-6xl">
                <div className="text-center py-12 text-gray-400">
                  <p>Desktop consolidated interface will be implemented</p>
                </div>
              </div>
            )}
          </motion.div>
        )}

        {currentStep === 2 && bookingSelection && (
          <motion.div
            key="step2"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="container mx-auto px-4 py-6 max-w-2xl"
          >
            {/* Step 2: Payment confirmation - preserved from original */}
            <div className="space-y-6">
              {/* Booking Summary */}
              <div className="bg-gray-800 rounded-xl p-6 border border-emerald-800/30">
                <h3 className="text-lg font-semibold text-white mb-4">Booking Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Date:</span>
                    <span className="text-white">{bookingSelection.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Slots:</span>
                    <span className="text-white">{bookingSelection.slots.join(', ')}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg pt-2 border-t border-gray-700">
                    <span className="text-gray-300">Total:</span>
                    <span className="text-emerald-400">₹{calculateTotalPrice()}</span>
                  </div>
                </div>
              </div>

              {/* Guest Details Form */}
              <div className="bg-gray-800 rounded-xl p-6 border border-emerald-800/30">
                <h3 className="text-lg font-semibold text-white mb-4">Guest Details</h3>
                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Full Name"
                    value={guestDetails.name}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                  <input
                    type="tel"
                    placeholder="Phone Number"
                    value={guestDetails.phone}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                  <input
                    type="text"
                    placeholder="Coupon Code (Optional)"
                    value={guestDetails.couponCode}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, couponCode: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                </div>
              </div>

              {/* Payment Button */}
              <button
                onClick={handlePayment}
                disabled={loading.booking || loading.payment}
                className="w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-600 text-white py-4 text-lg font-semibold rounded-lg transition-all min-h-[48px] flex items-center justify-center gap-2"
              >
                {loading.payment || loading.booking ? (
                  <Loader className="animate-spin" size={20} />
                ) : (
                  <>
                    <CreditCard size={20} />
                    Pay ₹{calculateTotalPrice()} & Confirm Booking
                  </>
                )}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success Modal */}
      <BookingSuccessModal
        isOpen={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          navigate('/profile');
        }}
        bookingReference={bookingReference}
      />
    </div>
  );
};

export default BookingPage;

  const canProceedToNextStep = () => {
    if (currentStep === 1) {
      // Enhanced validation including date validation
      const hasAllFields = selectedVenue && selectedSport && localSelectedCourt && localSelectedDate && localSelectedDate.trim() !== '';
      if (!hasAllFields) return false;

      // Validate date is within allowed range
      const dateValidation = validateBookingDate(localSelectedDate);
      return dateValidation.valid;
    } else if (currentStep === 2) {
      return selectedSlots.length > 0;
    }
    return true;
  };

  // Consecutive slot selection logic (copied from BookSlotModal)
  const timeToMinutes = useCallback((timeStr: string) => {
    const [time, period] = timeStr.split(' ');
    const [hours, minutes] = time.split(':').map(Number);
    let totalMinutes = (hours % 12) * 60 + minutes;
    if (period === 'PM' && hours !== 12) totalMinutes += 12 * 60;
    if (period === 'AM' && hours === 12) totalMinutes = minutes;
    return totalMinutes;
  }, []);

  const areSlotsContinuous = (slots: string[]) => {
    if (slots.length <= 1) return true;

    const sortedSlots = slots.sort((a, b) => {
      const startTimeA = a.split(' - ')[0];
      const startTimeB = b.split(' - ')[0];
      return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
    });

    for (let i = 0; i < sortedSlots.length - 1; i++) {
      const currentEndTime = sortedSlots[i].split(' - ')[1];
      const nextStartTime = sortedSlots[i + 1].split(' - ')[0];

      if (currentEndTime !== nextStartTime) {
        return false;
      }
    }
    return true;
  };

  // Removed unused findSlotGap function

  const wouldCreateValidSelection = (newSlot: string, currentSlots: string[]) => {
    if (currentSlots.length === 0) return true;

    const testSlots = [...currentSlots, newSlot];
    return areSlotsContinuous(testSlots);
  };

  const getSelectableSlots = (currentSlots: string[], availableSlots: TimeSlot[]) => {
    if (currentSlots.length === 0) {
      return availableSlots.map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`);
    }

    const sortedCurrentSlots = currentSlots.sort((a, b) => {
      const startTimeA = a.split(' - ')[0];
      const startTimeB = b.split(' - ')[0];
      return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
    });

    const firstSlotStart = sortedCurrentSlots[0].split(' - ')[0];
    const lastSlotEnd = sortedCurrentSlots[sortedCurrentSlots.length - 1].split(' - ')[1];

    return availableSlots
      .map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`)
      .filter(slotDisplay => {
        if (currentSlots.includes(slotDisplay)) return true;

        const slotStart = slotDisplay.split(' - ')[0];
        const slotEnd = slotDisplay.split(' - ')[1];

        return slotEnd === firstSlotStart || slotStart === lastSlotEnd;
      });
  };

  const getSlotsBetween = (startSlot: string, endSlot: string, availableSlots: TimeSlot[]) => {
    const startTime = timeToMinutes(startSlot.split(' - ')[0]);
    const endTime = timeToMinutes(endSlot.split(' - ')[1]);

    return availableSlots
      .map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`)
      .filter(slotDisplay => {
        const slotStartTime = timeToMinutes(slotDisplay.split(' - ')[0]);
        const slotEndTime = timeToMinutes(slotDisplay.split(' - ')[1]);
        return slotStartTime >= startTime && slotEndTime <= endTime;
      })
      .filter(slotDisplay => {
        const slot = availableSlots.find(s =>
          `${formatTime(s.start_time)} - ${formatTime(s.end_time)}` === slotDisplay
        );
        return slot?.is_available;
      });
  };

  const handleSlotClick = (slot: TimeSlot, event?: React.MouseEvent) => {
    if (!slot.is_available) return;

    const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
    const slotPrice = parseFloat(slot.price);

    // For capacity-based sports, only allow single slot selection
    if (slot.booking_type === 'capacity_based') {
      if (selectedSlots.includes(slotDisplay)) {
        setSelectedSlots([]);
        setSelectedSlotPrices({});
      } else {
        setSelectedSlots([slotDisplay]);
        setSelectedSlotPrices({ [slotDisplay]: slotPrice });
      }
      return;
    }

    // Enhanced logic for court-based sports with consecutive validation
    if (selectedSlots.includes(slotDisplay)) {
      const newSlots = selectedSlots.filter(s => s !== slotDisplay);
      const newSelectedSlotPrices = { ...selectedSlotPrices };
      delete newSelectedSlotPrices[slotDisplay];

      if (newSlots.length > 0 && !areSlotsContinuous(newSlots)) {
        toast({
          title: "Invalid selection",
          description: "Removing this slot would create a gap. Please select consecutive time slots only.",
          variant: "destructive",
        });
        return;
      }

      setSelectedSlots(newSlots);
      setSelectedSlotPrices(newSelectedSlotPrices);
    } else {
      // Check for range selection with Shift+Click
      if (event?.shiftKey && selectedSlots.length > 0) {
        const sortedCurrentSlots = selectedSlots.sort((a, b) => {
          const startTimeA = a.split(' - ')[0];
          const startTimeB = b.split(' - ')[0];
          return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
        });

        const firstSlot = sortedCurrentSlots[0];
        const lastSlot = sortedCurrentSlots[sortedCurrentSlots.length - 1];

        let rangeStart: string, rangeEnd: string;
        const clickedTime = timeToMinutes(slotDisplay.split(' - ')[0]);
        const firstTime = timeToMinutes(firstSlot.split(' - ')[0]);
        const lastTime = timeToMinutes(lastSlot.split(' - ')[0]);

        if (clickedTime < firstTime) {
          rangeStart = slotDisplay;
          rangeEnd = lastSlot;
        } else if (clickedTime > lastTime) {
          rangeStart = firstSlot;
          rangeEnd = slotDisplay;
        } else {
          rangeStart = firstSlot;
          rangeEnd = slotDisplay;
        }

        const rangeSlots = getSlotsBetween(rangeStart, rangeEnd, availableTimeSlots);

        if (areSlotsContinuous(rangeSlots)) {
          const newPrices = { ...selectedSlotPrices };
          rangeSlots.forEach(slot => {
            const slotData = availableTimeSlots.find(s =>
              `${formatTime(s.start_time)} - ${formatTime(s.end_time)}` === slot
            );
            if (slotData) {
              newPrices[slot] = parseFloat(slotData.price);
            }
          });

          setSelectedSlots(rangeSlots);
          setSelectedSlotPrices(newPrices);

          toast({
            title: "Range selected",
            description: `Selected ${rangeSlots.length} consecutive slots`,
          });
        } else {
          toast({
            title: "Invalid range",
            description: "Cannot select range with gaps or unavailable slots.",
            variant: "destructive",
          });
        }
        return;
      }

      // Regular single slot selection
      if (!wouldCreateValidSelection(slotDisplay, selectedSlots)) {
        // Show dismissible warning popup only once per session
        if (!nonConsecutiveWarningShown) {
          setShowNonConsecutiveWarning(true);
          setNonConsecutiveWarningShown(true);
        }
        return;
      }

      const updatedSlots = [...selectedSlots, slotDisplay];
      const sortedSlots = updatedSlots.sort((a, b) => {
        const startTimeA = a.split(' - ')[0];
        const startTimeB = b.split(' - ')[0];
        return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
      });

      setSelectedSlots(sortedSlots);
      setSelectedSlotPrices({
        ...selectedSlotPrices,
        [slotDisplay]: slotPrice
      });
    }
  };

  // Fetch availability function (updated with correct Supabase integration)
  const fetchAvailability = useCallback(async () => {
    if (!localSelectedCourt || !localSelectedDate) return;

    setLoading(prev => ({ ...prev, availability: true }));
    try {
      // Use the correct function signature - single court_id, not array
      const { data, error } = await supabase.rpc('get_unified_availability' as any, {
        p_court_id: localSelectedCourt,
        p_date: localSelectedDate
      });

      if (error) throw error;

      const slotsWithPrice = (data as any)?.map((slot: {
        start_time: string;
        end_time: string;
        is_available: boolean;
        available_spots: number;
        max_capacity: number;
        price: string;
        booking_type: string;
      }) => ({
        ...slot,
        price: slot.price || courtRate.toString(),
        booking_type: slot.booking_type || 'court_based'
      })) || [];

      setAvailableTimeSlots(slotsWithPrice);

      // Check if selected slots are still available (exact BookSlotModal logic)
      const updatedSelectedSlots = selectedSlots.filter(slotDisplay => {
        const [startTime, endTime] = slotDisplay.split(' - ').map(t => padTime(t));
        const slotStillAvailable = slotsWithPrice.some((slot: any) =>
          padTime(slot.start_time) === startTime &&
          padTime(slot.end_time) === endTime &&
          slot.is_available
        );
        if (!slotStillAvailable && selectedSlots.length > 0) {
          toast({
            title: "Slot no longer available",
            description: `The time slot ${slotDisplay} is no longer available`,
            variant: "destructive",
          });
          const updatedPrices = { ...selectedSlotPrices };
          delete updatedPrices[slotDisplay];
          setSelectedSlotPrices(updatedPrices);
        }
        return slotStillAvailable;
      });
      if (updatedSelectedSlots.length !== selectedSlots.length) {
        setSelectedSlots(updatedSelectedSlots);
      }
    } catch (error) {
      console.error('Error fetching availability:', error);
      toast({
        title: "Error",
        description: "Failed to load availability",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, availability: false }));
    }
  }, [localSelectedCourt, localSelectedDate, courtRate, selectedSlots, selectedSlotPrices, padTime]);

  // Additional useEffects for availability (match BookSlotModal pattern exactly)
  useEffect(() => {
    if (localSelectedCourt && localSelectedDate) {
      fetchAvailability();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localSelectedCourt, localSelectedDate, refreshKey]);

  useEffect(() => {
    if (localSelectedCourt) {
      fetchAvailability();
      fetchCourtDetails(localSelectedCourt);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localSelectedCourt, refreshKey]);

  useEffect(() => {
    // When user changes (login/logout), reset slot state and fetch fresh availability
    setSelectedSlots([]);
    setSelectedSlotPrices({});
    setAvailableTimeSlots([]);
    if (user && localSelectedCourt && localSelectedDate) {
      fetchAvailability();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Fetch user profile data (matching BookSlotModal pattern)
  useEffect(() => {
    if (user) {
      const fetchUserProfile = async () => {
        setProfileLoading(true);
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('id, full_name, phone, email')
            .eq('id', user.id)
            .maybeSingle(); // Use maybeSingle() to handle missing profiles

          if (error) throw error;

          if (data) {
            setUserProfile(data);
            setName(data.full_name || '');
            setPhone(data.phone || '');
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
          toast({
            title: "Profile Error",
            description: "Failed to load user profile data",
            variant: "destructive",
          });
        } finally {
          setProfileLoading(false);
        }
      };

      fetchUserProfile();
    }
  }, [user, toast]);

  // Security initialization useEffect
  useEffect(() => {
    const initializeSecurity = async () => {
      setSecurityLoading(true);

      try {
        // Verify authentication state
        if (!verifyAuthenticationState(user)) {
          setAuthVerified(false);
          setSecurityLoading(false);
          return;
        }

        // Generate CSRF token
        const token = generateCSRFToken();
        setCsrfToken(token);

        // Fetch Razorpay key securely from server (conservative approach)
        try {
          const { data: keyData, error: keyError } = await supabase.functions.invoke('get-razorpay-key');

          if (keyError || !keyData?.key_id) {
            console.error('Failed to fetch Razorpay key:', keyError);
            // Fallback to existing system if secure key fetch fails
            console.warn('Using fallback payment system');
            setRazorpayKeyId('rzp_test_fallback'); // This will be handled by existing create-razorpay-order
          } else {
            setRazorpayKeyId(keyData.key_id);
            // Razorpay key fetched securely (key details removed from logs)
          }
        } catch (keyFetchError) {
          console.error('Razorpay key fetch exception:', keyFetchError);
          // Fallback to existing system
          setRazorpayKeyId('rzp_test_fallback');
        }
        setAuthVerified(true);

        // Log security initialization
        await logSecurityEvent('BOOKING_PAGE_SECURITY_INITIALIZED', {
          user_id: user?.id,
          csrf_token_generated: true,
          razorpay_key_fetched: true
        }, user?.id);

      } catch (error) {
        console.error('Security initialization failed:', error);
        toast({
          title: "Security initialization failed",
          description: "Please refresh the page",
          variant: "destructive",
        });
        setAuthVerified(false);
      } finally {
        setSecurityLoading(false);
      }
    };

    if (user) {
      initializeSecurity();
    } else {
      setSecurityLoading(false);
      setAuthVerified(false);
    }
  }, [user, toast]);

  useEffect(() => {
    // When user navigates to the slot selection step, always fetch latest availability
    if (currentStep === 2 && localSelectedCourt && localSelectedDate) {
      debouncedSlotCheck(() => fetchAvailability());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentStep, localSelectedCourt, localSelectedDate, selectedCourtGroupId]);

  // Enhanced real-time subscription with proper error handling and connection management
  useEffect(() => {
    if (!user || !localSelectedCourt || !localSelectedDate) return;

    let bookingChannel: ReturnType<typeof supabase.channel> | null = null;
    let blockedSlotsChannel: ReturnType<typeof supabase.channel> | null = null;
    let isSubscribed = false;
    let retryTimeout: NodeJS.Timeout;
    let courtIdsToCheck = [localSelectedCourt];

    const setupSubscriptions = async () => {
      try {
        // Wait for authentication to be fully established
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          console.warn('No active session for real-time subscriptions');
          return;
        }



        // Get court group information if needed
        if (selectedCourtGroupId) {
          try {
            const { data: groupCourts, error } = await supabase
              .from('courts')
              .select('id')
              .eq('court_group_id', selectedCourtGroupId)
              .eq('is_active', true);

            if (!error && groupCourts) {
              courtIdsToCheck = groupCourts.map((c: { id: string }) => c.id);
            }
          } catch (error) {
            console.warn('Failed to fetch court group data for subscriptions');
            // Continue with single court
          }
        }

        // Create booking updates subscription with error handling
        const channelName = `booking-updates-${localSelectedCourt}-${Date.now()}`;

        bookingChannel = supabase
          .channel(channelName)
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'bookings',
            filter: courtIdsToCheck.length > 1
              ? `court_id=in.(${courtIdsToCheck.join(',')}),booking_date=eq.${localSelectedDate}`
              : `court_id=eq.${localSelectedCourt},booking_date=eq.${localSelectedDate}`
          }, (payload) => {
            // Real-time booking update received - refresh availability
            if (payload.eventType) {
              debouncedSlotCheck(() => fetchAvailability());
            }
          })
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              isSubscribed = true;
              // Real-time booking subscription established
            } else if (status === 'CHANNEL_ERROR') {
              console.warn('Booking channel subscription error, will retry');
              scheduleRetry();
            } else if (status === 'TIMED_OUT') {
              console.warn('Booking channel subscription timed out, will retry');
              scheduleRetry();
            }
          });

        // Create blocked slots subscription with error handling
        const blockedSlotsChannelName = `blocked-slots-updates-${localSelectedCourt}-${Date.now()}`;

        blockedSlotsChannel = supabase
          .channel(blockedSlotsChannelName)
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'blocked_slots',
            filter: courtIdsToCheck.length > 1
              ? `court_id=in.(${courtIdsToCheck.join(',')}),date=eq.${localSelectedDate}`
              : `court_id=eq.${localSelectedCourt},date=eq.${localSelectedDate}`
          }, (payload) => {
            // Real-time blocked slots update received - refresh availability
            if (payload.eventType) {
              debouncedSlotCheck(() => fetchAvailability());
            }
          })
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              // Real-time blocked slots subscription established
            } else if (status === 'CHANNEL_ERROR') {
              console.warn('Blocked slots channel subscription error, will retry');
              scheduleRetry();
            } else if (status === 'TIMED_OUT') {
              console.warn('Blocked slots channel subscription timed out, will retry');
              scheduleRetry();
            }
          });

      } catch (error) {
        console.error('❌ Failed to setup real-time subscriptions:', error);

        // Check if it's a WebSocket connection error
        if (error instanceof Error) {
          if (error.message.includes('WebSocket') || error.message.includes('connection')) {
            console.error('🔌 WebSocket connection issue detected:', error.message);
          }
        }

        scheduleRetry();
      }
    };

    const scheduleRetry = () => {
      if (retryTimeout) clearTimeout(retryTimeout);
      retryTimeout = setTimeout(() => {
        if (!isSubscribed) {
          cleanup();
          setupSubscriptions();
        }
      }, 5000); // Retry after 5 seconds
    };

    const cleanup = () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }

      if (bookingChannel) {
        try {
          supabase.removeChannel(bookingChannel);
        } catch (error) {
          console.warn('Error removing booking channel:', error);
        }
        bookingChannel = null;
      }

      if (blockedSlotsChannel) {
        try {
          supabase.removeChannel(blockedSlotsChannel);
        } catch (error) {
          console.warn('Error removing blocked slots channel:', error);
        }
        blockedSlotsChannel = null;
      }

      isSubscribed = false;
    };

    // Setup subscriptions with a small delay to ensure component is fully mounted
    // Increased delay to ensure authentication is fully established
    const initTimeout = setTimeout(setupSubscriptions, 2000);

    return () => {
      clearTimeout(initTimeout);
      cleanup();
    };
  }, [user, localSelectedCourt, localSelectedDate, selectedCourtGroupId, fetchAvailability]);

  // Price calculation functions
  const calculateTotalPrice = useCallback(() => {
    return Object.values(selectedSlotPrices).reduce((total, price) => total + price, 0);
  }, [selectedSlotPrices]);

  const getFinalPrice = useCallback(() => {
    if (couponValidation && couponValidation.valid && couponValidation.final_price !== undefined) {
      return couponValidation.final_price;
    }
    return calculateTotalPrice();
  }, [couponValidation, calculateTotalPrice]);

  const getDiscountAmount = () => {
    if (couponValidation && couponValidation.valid && couponValidation.discount_amount !== undefined) {
      return couponValidation.discount_amount;
    }
    return 0;
  };

  // Secure coupon validation function with rate limiting
  const validateCoupon = useCallback(async (code: string) => {
    if (!code.trim() || !selectedVenue || !user?.id || !authVerified) return;

    // Check rate limiting
    if (!checkCouponRateLimit(user.id)) {
      return;
    }

    setCouponLoading(true);
    try {
      // Sanitize coupon code
      const sanitizedCode = sanitizeCouponCode(code);
      if (!sanitizedCode) {
        toast({
          title: "Invalid coupon format",
          description: "Please enter a valid coupon code",
          variant: "destructive",
        });
        return;
      }

      const originalPrice = calculateTotalPrice();

      // Use enhanced coupon validation with rate limiting (conservative approach)
      let data, error;
      try {
        // Try the new secure function first
        const result = await supabase.rpc('validate_coupon_with_security', {
          p_coupon_code: sanitizedCode,
          p_venue_id: selectedVenue,
          p_original_price: originalPrice,
          p_user_id: user.id
        });
        data = result.data;
        error = result.error;
      } catch (secureError) {
        console.warn('Secure coupon validation failed, using fallback:', secureError);
        // Fallback to existing function
        const fallbackResult = await supabase.rpc('validate_and_apply_coupon', {
          p_coupon_code: sanitizedCode,
          p_venue_id: selectedVenue,
          p_original_price: originalPrice,
          p_user_id: user.id
        });
        data = fallbackResult.data;
        error = fallbackResult.error;
      }

      if (error) throw error;

      setCouponValidation(data as any);

      if ((data as any)?.valid) {
        toast({
          title: "Coupon applied!",
          description: `You saved ₹${(data as any).discount_amount}`,
        });

        // Log successful coupon application
        await logSecurityEvent('COUPON_APPLIED_SUCCESS', {
          coupon_code: sanitizedCode,
          discount_amount: (data as any).discount_amount,
          original_price: originalPrice
        }, user.id);
      } else if ((data as any)?.rate_limited) {
        // Rate limit handled by server
        return;
      } else {
        toast({
          title: "Invalid coupon",
          description: (data as any)?.error || "This coupon is not valid",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error validating coupon:', error);
      toast({
        title: "Error",
        description: "Failed to validate coupon",
        variant: "destructive",
      });
      setCouponValidation({ valid: false, error: "Validation failed" });

      // Log coupon validation error
      await logSecurityEvent('COUPON_VALIDATION_ERROR', {
        error: error instanceof Error ? error.message : 'Unknown error',
        coupon_code: sanitizeCouponCode(code)
      }, user?.id);
    } finally {
      setCouponLoading(false);
    }
  }, [selectedVenue, user?.id, calculateTotalPrice, authVerified]);

  // Enhanced payment order creation with security logging
  const createRazorpayOrder = useCallback(async () => {
    if (!user?.id) {
      toast({
        title: "Authentication required",
        description: "Please log in to continue",
        variant: "destructive",
      });
      return null;
    }

    // Loading state is now managed by handlePayment function
    try {
      const finalPrice = getFinalPrice();
      const totalAmountInPaise = Math.round(finalPrice * 100);
      const receipt = `booking_${Date.now()}`;

      // Enhanced request with security data (processed by enhanced Edge Function)
      const response = await supabase.functions.invoke('create-razorpay-order', {
        body: {
          amount: totalAmountInPaise,
          receipt: receipt,
          notes: {
            court_id: localSelectedCourt,
            date: localSelectedDate,
            slots: selectedSlots.join(', '),
            venueId: selectedVenue,
            sportId: selectedSport,
            user_id: user.id,
            guest_name: name,
            guest_phone: phone
          },
          // Additional security fields for enhanced Edge Function
          courtId: localSelectedCourt,
          date: localSelectedDate,
          couponCode: couponCode,
          guestName: name,
          guestPhone: phone,
          csrfToken: csrfToken
        }
      });

      if (response.error) {
        console.error('Payment order creation failed:', response.error);
        throw response.error;
      }

      const { order, key_id } = response.data;
      // Payment order created successfully (order ID removed from logs for security)

      return {
        order,
        key_id
      };
    } catch (error) {
      console.error('Error creating payment order:', error);
      toast({
        title: "Payment Error",
        description: "Could not initialize payment",
        variant: "destructive",
      });
      return null;
    }
    // Loading state is managed by handlePayment function
  }, [user?.id, localSelectedCourt, localSelectedDate, selectedSlots, couponCode, name, phone, csrfToken, getFinalPrice, selectedVenue, selectedSport]);

  // Enhanced function to log payment data via edge function (matching BookSlotModal)
  const logPaymentData = async (paymentId: string, orderId: string, bookingIds: string[] = []) => {
    // Payment data logging initiated (sensitive data removed from logs)

    try {
      const totalPrice = calculateTotalPrice();
      // Total price calculated (amount removed from logs for security)

      // Call the edge function to log payment data
      const { data, error } = await supabase.functions.invoke('log-payment-data', {
        body: {
          paymentId,
          orderId,
          amount: totalPrice,
          courtId: localSelectedCourt,
          date: localSelectedDate,
          slots: selectedSlots,
          venueId: selectedVenue,
          sportId: selectedSport,
          userId: user?.id,
          bookingIds
        }
      });

      if (error) {
        console.error('❌ Error calling log-payment-data function:', error);
        toast({
          title: "Payment logging failed",
          description: "Failed to log payment data: " + error.message,
          variant: "destructive",
        });
      } else {
        // Payment data logged successfully (details removed from logs for security)
        if (data.success) {
          toast({
            title: "Booking Successful, Thank you!",
            description: "Your booking has been confirmed and payment processed",
            variant: "default",
          });
        } else {
          console.error('❌ Function returned error:', data.error);
          toast({
            title: "Payment logging failed",
            description: "Function error: " + data.error,
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error('💥 Exception in logPaymentData:', error);
      toast({
        title: "Payment logging failed",
        description: "Exception: " + error.message,
        variant: "destructive",
      });
    }
  };

  // Secure booking submission with payment verification
  const handleBooking = useCallback(async (paymentId: string, orderId: string, signature: string) => {
    if (!authVerified || !localSelectedCourt || !localSelectedDate || selectedSlots.length === 0 || !user?.id) {
      toast({
        title: "Missing information",
        description: "Please complete all required fields and ensure authentication",
        variant: "destructive",
      });
      return;
    }

    setLoading(prev => ({ ...prev, booking: true }));
    try {
      // Starting booking creation (payment IDs removed from logs for security)

      // Conservative approach: Skip complex payment verification for now
      // The existing system already handles payment verification adequately

      // Calculate start and end times from selected slots
      const sortedSlots = selectedSlots.sort((a, b) => {
        const startTimeA = a.split(' - ')[0];
        const startTimeB = b.split(' - ')[0];
        return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
      });

      const startTime = padTime(sortedSlots[0].split(' - ')[0]);
      const endTime = padTime(sortedSlots[sortedSlots.length - 1].split(' - ')[1]);
      const totalPrice = getFinalPrice();

      // Step 3: Sanitize inputs before booking creation
      const sanitizedGuestName = name ? sanitizeBookingInput(name) : null;
      const sanitizedGuestPhone = phone ? sanitizePhoneNumber(phone) : null;
      const sanitizedCouponCode = couponValidation?.valid ? sanitizeCouponCode(couponCode) : null;

      const { data, error } = await supabase.rpc('create_booking_with_coupon', {
        p_court_id: localSelectedCourt,
        p_user_id: user.id,
        p_booking_date: localSelectedDate,
        p_start_time: startTime,
        p_end_time: endTime,
        p_original_price: calculateTotalPrice(),
        p_coupon_code: sanitizedCouponCode,
        p_guest_name: sanitizedGuestName,
        p_guest_phone: sanitizedGuestPhone,
        p_payment_reference: paymentId,
        p_payment_status: 'completed',
        p_payment_method: 'online'
      });

      if (error) throw error;

      if ((data as any)?.success) {
        // Booking created successfully (booking ID removed from logs for security)

        // Booking creation response structure logging removed for production security

        // Log payment data after successful booking creation (matching BookSlotModal)
        const bookingIds = [(data as any).booking_id];
        await logPaymentData(paymentId, orderId, bookingIds);
        // Payment data logged successfully

        // Get the actual booking reference from the database response
        // The booking reference should be returned by the create_booking_with_coupon function
        let actualBookingReference = (data as any).booking_reference ||
                                   (data as any).reference ||
                                   (data as any).booking_ref ||
                                   null;

        // If booking reference is not in the response, fetch it from the database using the booking ID
        if (!actualBookingReference && (data as any).booking_id) {
          try {
            // Fetching booking reference from database - booking ID removed for production security
            const { data: bookingData, error: fetchError } = await supabase
              .from('bookings')
              .select('booking_reference')
              .eq('id', (data as any).booking_id)
              .single();

            if (!fetchError && bookingData?.booking_reference) {
              actualBookingReference = bookingData.booking_reference;
              // Retrieved booking reference from database - reference removed for production security
            } else {
              // Failed to fetch booking reference - error details removed for production security
            }
          } catch (fetchError) {
            // Error fetching booking reference - error details removed for production security
          }
        }

        if (!actualBookingReference) {
          // No booking reference found - response structure details removed for production security
        }

        // Prepare booking details for calendar integration
        const bookingDetails = {
          booking_reference: actualBookingReference || `GR2P-FALLBACK-${Date.now()}`,
          guest_name: name || user?.email?.split('@')[0] || 'Guest',
          guest_phone: phone || '',
          venue_name: venueDetails?.name || 'Grid२Play Venue',
          venue_location: venueDetails?.location || 'Location not available',
          court_name: courtDetails?.name || 'Court',
          sport_name: sportDetails?.name || 'Sport',
          booking_date: localSelectedDate,
          start_time: sortedSlots[0].split(' - ')[0],
          end_time: sortedSlots[sortedSlots.length - 1].split(' - ')[1],
          total_price: getFinalPrice().toString(),
          payment_reference: paymentId
        };

        // Show booking success modal with calendar integration
        setBookingSuccessDetails(bookingDetails);
        setShowBookingSuccess(true);

        // Trigger availability refresh in parent components (matching BookSlotModal)
        if (onBookingComplete) {
          onBookingComplete();
        }

        // Dispatch custom event for global availability refresh (matching BookSlotModal)
        window.dispatchEvent(new CustomEvent('bookingCompleted'));

        // Reset form
        setSelectedSlots([]);
        setSelectedSlotPrices({});
        setCouponCode('');
        setCouponValidation(null);
        setName('');
        setPhone('');
      } else {
        throw new Error((data as any)?.error || 'Booking failed');
      }
    } catch (error) {
      console.error('Error creating booking:', error);

      // Still log payment data even if booking failed (matching BookSlotModal)
      await logPaymentData(paymentId, orderId, []);
      // Payment data logged despite booking failure

      toast({
        title: "Booking failed",
        description: error instanceof Error ? error.message : "Failed to create booking",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, booking: false }));
    }
  }, [localSelectedCourt, localSelectedDate, selectedSlots, user?.id, name, phone, couponCode, couponValidation, getFinalPrice, calculateTotalPrice, padTime, timeToMinutes, navigate, selectedVenue, selectedSport, toast]);

  // Secure payment handling with server-side key management
  const handlePayment = useCallback(async () => {
    if (!authVerified || !user || !localSelectedCourt || !localSelectedDate || selectedSlots.length === 0) {
      toast({
        title: "Missing information",
        description: "Please complete all booking details and ensure authentication",
        variant: "destructive",
      });
      return;
    }

    // Prevent multiple payment attempts
    if (loading.payment || loading.booking) {
      toast({
        title: "Payment in progress",
        description: "Please wait for the current payment to complete",
        variant: "destructive",
      });
      return;
    }

    // Set payment loading state at the start
    setLoading(prev => ({ ...prev, payment: true }));

    try {
      const orderData = await createRazorpayOrder();
      if (!orderData) {
        // Reset loading state if order creation failed
        setLoading(prev => ({ ...prev, payment: false }));
        return;
      }

      const { order, key_id } = orderData;

      const options = {
        key: razorpayKeyId || key_id, // Use securely fetched key or fallback to response key
        amount: order.amount,
        currency: order.currency,
        name: venues.find(v => v.id === selectedVenue)?.name || "Grid२Play",
        description: `Court Booking for ${localSelectedDate}`,
        order_id: order.id,
        prefill: {
          name: sanitizeBookingInput(name || user.email || ''),
          email: user.email,
          contact: sanitizePhoneNumber(phone || '')
        },
        notes: {
          court_id: localSelectedCourt,
          date: localSelectedDate,
          slots: selectedSlots.join(', '),
          venueId: selectedVenue,
          sportId: selectedSport,
          user_id: user?.id,
          csrf_token: csrfToken
        },
        theme: {
          color: "#047857" // Emerald-800
        },
        handler: function(response: any) {
          // Conservative approach - maintain existing signature handling
          handleBooking(
            response.razorpay_payment_id,
            response.razorpay_order_id,
            response.razorpay_signature || ''
          );
        },
        modal: {
          ondismiss: function() {
            // Reset payment loading state when modal is dismissed
            setLoading(prev => ({ ...prev, payment: false }));

            toast({
              title: "Payment Cancelled",
              description: "Your booking was not completed",
              variant: "destructive",
            });

            // Log payment cancellation
            logSecurityEvent('PAYMENT_CANCELLED', {
              order_id: order.id,
              amount: order.amount / 100 // Convert paise to rupees
            }, user?.id);
          },
          // Prevent automatic form submission
          escape: false,
          backdropclose: false
        }
      };

      const razorpayInstance = new window.Razorpay(options);

      // Log payment initiation
      await logSecurityEvent('PAYMENT_INITIATED', {
        order_id: order.id,
        amount: order.amount / 100, // Convert paise to rupees
        court_id: localSelectedCourt
      }, user.id);

      // Add a small delay to ensure proper initialization
      setTimeout(() => {
        razorpayInstance.open();
      }, 100);

    } catch (error) {
      console.error("Secure payment initialization error:", error);

      // Log payment initialization error
      await logSecurityEvent('PAYMENT_INITIALIZATION_ERROR', {
        error: error instanceof Error ? error.message : 'Unknown error',
        court_id: localSelectedCourt
      }, user?.id);

      toast({
        title: "Payment Error",
        description: "Failed to initialize secure payment",
        variant: "destructive",
      });
    } finally {
      // Ensure payment loading state is reset in case of any errors
      // Note: This will be overridden by the modal ondismiss or successful payment flow
      setTimeout(() => {
        setLoading(prev => ({ ...prev, payment: false }));
      }, 1000); // Small delay to allow for proper payment flow
    }
  }, [user, localSelectedCourt, localSelectedDate, selectedSlots, loading.payment, loading.booking, createRazorpayOrder, venues, selectedVenue, selectedSport, name, phone, handleBooking]);

  // Duplicate handleBooking function removed - using the one defined earlier

  // Security loading guard - prevent any actions before security initialization
  if (securityLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-emerald-900 text-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-400 mx-auto" />
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-emerald-400">Initializing Security</h3>
            <p className="text-gray-400">Setting up secure booking environment...</p>
          </div>
        </div>
      </div>
    );
  }

  // Authentication guard
  if (!authVerified) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-emerald-900 text-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-red-400 text-6xl mb-4">🔒</div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-red-400">Authentication Required</h3>
            <p className="text-gray-400">Please log in to access the booking system</p>
            <button
              onClick={() => navigate('/auth')}
              className="mt-4 px-6 py-2 bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
            >
              Go to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="min-h-screen bg-gray-900 text-white"
    >
      {/* Header */}
      <div className="sticky top-0 z-50 bg-gradient-to-b from-gray-900 to-gray-900/90 backdrop-blur-sm border-b border-emerald-800/20">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                type="button"
                onClick={handleBack}
                className="text-gray-400 hover:text-white transition-colors p-1 rounded-full hover:bg-gray-800/50"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                  <Clock className="text-emerald-400" size={24} />
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 to-emerald-600">
                    Book Your Slot
                  </span>
                </h1>
                <p className="text-sm text-gray-400">Step {currentStep} of 3</p>
              </div>
            </div>

            {/* Progress indicator */}
            <div className="hidden sm:flex items-center gap-2">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all ${
                      currentStep === step
                        ? 'bg-emerald-600 text-white shadow-lg shadow-emerald-800/30'
                        : currentStep > step
                        ? 'bg-emerald-800/80 text-emerald-200'
                        : 'bg-gray-800 text-gray-400'
                    }`}
                  >
                    {currentStep > step ? (
                      <Check size={18} className="text-emerald-200" />
                    ) : (
                      step
                    )}
                  </div>
                  {step < 3 && (
                    <div className={`w-16 h-1 transition-all duration-300 ${
                      currentStep > step ? 'bg-gradient-to-r from-emerald-600 to-emerald-800' : 'bg-gray-700'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Step labels */}
          <div className="flex justify-between mt-3 text-sm px-4">
            <span className={currentStep === 1 ? 'text-emerald-400 font-medium' : 'text-gray-500'}>Details</span>
            <span className={currentStep === 2 ? 'text-emerald-400 font-medium' : 'text-gray-500'}>Slots</span>
            <span className={currentStep === 3 ? 'text-emerald-400 font-medium' : 'text-gray-500'}>Confirm</span>
          </div>
        </div>
      </div>

      {/* Loading Overlay for Booking/Payment Process */}
      {(loading.booking || loading.payment) && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-gray-900 p-6 rounded-xl border border-emerald-500/20 text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="mx-auto mb-4"
            >
              <Clock className="text-emerald-400" size={32} />
            </motion.div>
            <h3 className="text-lg font-semibold text-emerald-400 mb-2">
              {loading.payment ? 'Initializing Payment...' : 'Processing Booking...'}
            </h3>
            <p className="text-gray-400 text-sm">
              {loading.payment ? 'Please wait while we set up your payment' : 'Creating your booking, please wait...'}
            </p>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-4xl booking-form-container">
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <motion.div
              key="step1"
              variants={stepVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="space-y-4 sm:space-y-6"
            >
              {/* Step 1: Venue, Sport, Court, Date Selection */}
              <div className="space-y-4 sm:space-y-6">
                {/* Venue Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <MapPin size={16} className="text-emerald-400" />
                    Select Venue
                  </label>
                  {loading.venues ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400" />
                      <span className="ml-2 text-gray-400">Loading venues...</span>
                    </div>
                  ) : (
                    <select
                      value={selectedVenue}
                      onChange={(e) => {
                        setSelectedVenue(e.target.value);
                        if (e.target.value) {
                          fetchVenueDetails(e.target.value);
                        }
                      }}
                      className="w-full p-2.5 sm:p-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-white placeholder-gray-400 transition-all"
                      disabled={loading.venues || !!initialVenueId}
                    >
                      <option value="" className="bg-gray-800">Select a venue</option>
                      {venues.map(venue => (
                        <option key={venue.id} value={venue.id} className="bg-gray-800">{venue.name}</option>
                      ))}
                    </select>
                  )}
                  {loading.venues && (
                    <div className="mt-1 text-xs text-gray-500 flex items-center gap-1">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                      Loading venues...
                    </div>
                  )}

                  {/* Venue Details */}
                  {venueDetails && (
                    <div className="mt-2 p-2.5 sm:p-3 bg-gray-800/50 rounded-lg border border-emerald-800/20">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-full bg-emerald-900/50 flex items-center justify-center">
                          <MapPin size={16} className="text-emerald-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-emerald-400">{venueDetails.name}</h4>
                          <p className="text-xs text-gray-400 mt-1">Select a sport available at this venue</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Sport Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Activity size={16} className="text-emerald-400" />
                    Select Sport
                  </label>

                  {!selectedVenue ? (
                    <p className="mt-1 text-xs text-gray-500 flex items-center gap-1">
                      <Info size={14} />
                      Please select a venue first
                    </p>
                  ) : venueSports.length === 0 ? (
                    <p className="mt-1 text-xs text-red-400 flex items-center gap-1">
                      <AlertCircle size={14} />
                      No sports available for this venue
                    </p>
                  ) : (
                    <div className="grid grid-cols-2 gap-2 sm:gap-3">
                      {venueSports.map((sport) => (
                        <div key={sport.id} className="relative">
                          <input
                            type="radio"
                            id={`sport-${sport.id}`}
                            name="sport"
                            value={sport.id}
                            checked={selectedSport === sport.id}
                            onChange={() => setSelectedSport(sport.id)}
                            className="sr-only"
                          />
                          <label
                            htmlFor={`sport-${sport.id}`}
                            className={`flex items-center justify-center gap-1 w-full p-2 sm:p-2.5 rounded-lg border text-xs sm:text-sm transition-all cursor-pointer min-h-[44px] ${
                              selectedSport === sport.id
                                ? 'bg-emerald-600 text-white border-emerald-700 shadow-md shadow-emerald-800/20'
                                : 'bg-gray-800 border-gray-600 hover:border-emerald-500 hover:bg-gray-750 text-gray-200'
                            }`}
                          >
                            <div className={`w-3 h-3 rounded-full border-2 transition-all ${
                              selectedSport === sport.id
                                ? 'border-white bg-white'
                                : 'border-gray-400'
                            }`}>
                              {selectedSport === sport.id && (
                                <div className="w-1.5 h-1.5 rounded-full bg-emerald-600"></div>
                              )}
                            </div>
                            <span>{sport.name}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Sport Details */}
                  {sportDetails && (
                    <div className="mt-2 p-2.5 sm:p-3 bg-gray-800/50 rounded-lg border border-emerald-800/20">
                      <div className="flex items-start gap-3">
                        {sportDetails.icon_name && (
                          <div className="w-10 h-10 rounded-full bg-emerald-900/50 flex items-center justify-center">
                            <span className="text-emerald-400">{sportDetails.icon_name}</span>
                          </div>
                        )}
                        <div>
                          <h4 className="font-medium text-emerald-400">
                            {selectedVenue ? (
                              <SportDisplayName
                                venueId={selectedVenue}
                                sportId={sportDetails.id}
                                defaultName={sportDetails.name}
                              />
                            ) : (
                              sportDetails.name
                            )}
                          </h4>
                          <p className="text-xs text-gray-400 mt-1">Select a court for this sport</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Court Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <MapPin size={16} className="text-emerald-400" />
                    Select Court
                  </label>

                  {loading.courts ? (
                    <div className="mt-1 text-xs text-gray-500 flex items-center gap-1">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                      Loading courts...
                    </div>
                  ) : !selectedVenue || !selectedSport ? (
                    <p className="mt-1 text-xs text-gray-500 flex items-center gap-1">
                      <Info size={14} />
                      Please select venue and sport first
                    </p>
                  ) : courts.length === 0 ? (
                    <p className="mt-1 text-xs text-red-400 flex items-center gap-1">
                      <AlertCircle size={14} />
                      No courts available for this sport
                    </p>
                  ) : (
                    <div className="grid grid-cols-2 gap-2 sm:gap-3">
                      {courts.map((court) => (
                        <div key={court.id} className="relative">
                          <input
                            type="radio"
                            id={`court-${court.id}`}
                            name="court"
                            value={court.id}
                            checked={localSelectedCourt === court.id}
                            onChange={() => handleCourtSelection(court.id)}
                            className="sr-only"
                          />
                          <label
                            htmlFor={`court-${court.id}`}
                            className={`flex items-center justify-center gap-1 w-full p-2 sm:p-2.5 rounded-lg border text-xs sm:text-sm transition-all cursor-pointer min-h-[44px] ${
                              localSelectedCourt === court.id
                                ? 'bg-emerald-600 text-white border-emerald-700 shadow-md shadow-emerald-800/20'
                                : 'bg-gray-800 border-gray-600 hover:border-emerald-500 hover:bg-gray-750 text-gray-200'
                            }`}
                          >
                            <div className={`w-3 h-3 rounded-full border-2 transition-all ${
                              localSelectedCourt === court.id
                                ? 'border-white bg-white'
                                : 'border-gray-400'
                            }`}>
                              {localSelectedCourt === court.id && (
                                <div className="w-1.5 h-1.5 rounded-full bg-emerald-600"></div>
                              )}
                            </div>
                            <span className="text-center">
                              {court.name}
                              {court.court_group_id && (
                                <span className="text-xs">🏟️</span>
                              )}
                            </span>
                          </label>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Court Details */}
                  {courtDetails && (
                    <div className="mt-2 p-2.5 sm:p-3 bg-gray-800/50 rounded-lg border border-emerald-800/20">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-full bg-emerald-900/50 flex items-center justify-center">
                          <span className="text-emerald-400">🏟️</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-emerald-400">{courtDetails.name}</h4>
                          {courtDetails.description && (
                            <p className="text-xs text-gray-400 mt-1">{courtDetails.description}</p>
                          )}
                          <div className="mt-2 flex items-center gap-2">
                            {courtDetails.court_group_id && (
                              <span className="text-xs px-2 py-1 bg-blue-900/30 text-blue-300 rounded-full">
                                Shared Space
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Date Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Calendar size={16} className="text-emerald-400" />
                    Select Date
                  </label>
                  <input
                    type="date"
                    value={localSelectedDate}
                    onChange={(e) => {
                      const newDate = e.target.value;
                      setLocalSelectedDate(newDate);

                      // Validate date and show error if invalid
                      const validation = validateBookingDate(newDate);
                      if (!validation.valid && newDate) {
                        toast({
                          title: "Invalid Date",
                          description: validation.message,
                          variant: "destructive",
                        });
                      }
                    }}
                    min={getMinBookingDate()}
                    max={getMaxBookingDate(userRole)}
                    className="w-full p-2.5 sm:p-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-white placeholder-gray-400 transition-all min-h-[48px] touch-manipulation"
                    style={{ fontSize: '16px', WebkitTapHighlightColor: 'transparent' }}
                  />
                  {localSelectedDate && (() => {
                    const validation = validateBookingDate(localSelectedDate);
                    if (!validation.valid) {
                      return (
                        <div className="mobile-error-message bg-red-900/20 border border-red-800/50 rounded-lg p-3 mt-2">
                          <p className="text-red-300 text-sm flex items-center gap-2">
                            <AlertCircle size={16} className="flex-shrink-0" />
                            <span>{validation.message}</span>
                          </p>
                        </div>
                      );
                    }
                    return null;
                  })()}
                </div>
              </div>
            </motion.div>
          )}

          {/* Step 2: Slot Selection */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              variants={stepVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="space-y-4 sm:space-y-6"
            >
              {/* Booking Summary */}
              <div className="bg-gray-800 rounded-xl p-4 sm:p-6 border border-emerald-800/30">
                <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 flex items-center gap-2">
                  <Clock className="text-emerald-400" size={18} />
                  Select Time Slots
                </h2>

                {/* Booking Details Summary */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-700/50 rounded-lg">
                  <div className="text-center">
                    <div className="text-xs sm:text-sm text-gray-400">Venue</div>
                    <div className="font-medium text-white text-sm sm:text-base">{venueDetails?.name || 'Loading...'}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs sm:text-sm text-gray-400">Sport</div>
                    <div className="font-medium text-white text-sm sm:text-base">{sportDetails?.name || 'Loading...'}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs sm:text-sm text-gray-400">Date</div>
                    <div className="font-medium text-white text-sm sm:text-base">
                      {localSelectedDate ? format(parseISO(localSelectedDate), 'MMM dd, yyyy') : 'Loading...'}
                    </div>
                  </div>
                </div>

                {/* Refresh Button */}
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium text-gray-300">Available Time Slots</h3>
                  <button
                    type="button"
                    onClick={() => setRefreshKey(prev => prev + 1)}
                    disabled={loading.availability}
                    className={`text-sm flex items-center gap-1 transition-colors ${loading.availability ? 'text-gray-500 cursor-not-allowed' : 'text-emerald-400 hover:text-emerald-300'}`}
                  >
                    <RefreshCw size={14} className={loading.availability ? 'animate-spin' : ''} />
                    Refresh
                  </button>
                </div>

                {/* Consecutive Slot Selection Info */}
                {availableTimeSlots.some(slot => slot.booking_type === 'court_based') && (
                  <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 bg-blue-900/20 rounded-lg border border-blue-800/30">
                    <div className="flex items-start gap-2">
                      <div className="w-4 h-4 bg-blue-500 rounded-full mt-0.5 flex-shrink-0"></div>
                      <div>
                        <h4 className="text-sm font-medium text-blue-300 mb-1">Consecutive Slot Selection</h4>
                        <p className="text-xs text-gray-400 mb-2">
                          You can select multiple time slots, but they must be consecutive (no gaps).
                          Slots that would create gaps are automatically disabled.
                        </p>
                        <p className="text-xs text-blue-300">
                          💡 <strong>Tip:</strong> Hold Shift and click to select a range of consecutive slots
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Slot Grid */}
                {loading.availability || loading.booking || loading.payment ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Clock className="text-emerald-400" size={32} />
                    </motion.div>
                    <p className="mt-4 text-gray-400">
                      {loading.booking ? 'Processing booking...' :
                       loading.payment ? 'Initializing payment...' :
                       'Loading available slots...'}
                    </p>
                  </div>
                ) : availableTimeSlots.length === 0 ? (
                  <div className="text-center py-12 bg-gray-800/30 rounded-xl border border-gray-700">
                    <AlertCircle className="mx-auto text-gray-400 mb-4" size={48} />
                    <h3 className="text-lg font-medium text-gray-300 mb-2">No Slots Available</h3>
                    <p className="text-gray-400">No time slots are available for the selected date.</p>
                  </div>
                ) : (
                  <div className="space-y-3 sm:space-y-4">
                    {/* Mobile-optimized grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
                      {availableTimeSlots.map((slot, index) => {
                        const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
                        const isSelected = selectedSlots.includes(slotDisplay);
                        const selectableSlots = getSelectableSlots(selectedSlots, availableTimeSlots);
                        const isSelectable = slot.is_available && (selectedSlots.length === 0 || selectableSlots.includes(slotDisplay));
                        const isDisabledByRule = slot.is_available && !isSelected && !isSelectable;

                        return (
                          <motion.button
                            key={`${slot.start_time}-${slot.end_time}`}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                            whileHover={{ scale: isSelectable ? 1.02 : 1 }}
                            whileTap={{ scale: isSelectable ? 0.98 : 1 }}
                            disabled={!isSelectable}
                            onClick={(e) => handleSlotClick(slot, e)}
                            className={`
                              p-2 sm:p-2.5 rounded-lg border transition-all text-center text-sm transform min-h-[44px] flex flex-col justify-between
                              ${!slot.is_available
                                ? 'bg-red-900/60 border-red-800 text-red-200 cursor-not-allowed'
                                : isSelected
                                  ? 'bg-emerald-600 text-white border-emerald-700 shadow-lg shadow-emerald-800/30'
                                  : isDisabledByRule
                                    ? 'bg-gray-900/60 border-gray-700 text-gray-500 cursor-not-allowed opacity-50'
                                    : 'bg-gray-800 border-gray-600 hover:border-emerald-500 hover:bg-gray-750 text-gray-200'
                              }
                              ${isSelected ? 'ring-2 ring-emerald-400' : ''}
                            `}
                          >
                            {/* Time and Price Row */}
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="font-semibold text-sm sm:text-base">
                                  {formatTime(slot.start_time)}
                                </div>
                                <div className="text-xs sm:text-sm opacity-75">
                                  to {formatTime(slot.end_time)}
                                </div>
                              </div>

                              <div className="text-right">
                                <div className="font-bold text-sm sm:text-base">
                                  ₹{parseFloat(slot.price).toFixed(0)}
                                </div>
                                {slot.booking_type === 'capacity_based' && (
                                  <div className="text-xs opacity-75 flex items-center gap-1">
                                    <User size={10} />
                                    {slot.available_spots || 0}/{slot.max_capacity || 0}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Status Indicators */}
                            <div className="flex justify-between items-center mt-1.5 sm:mt-2">
                              <div className="flex items-center gap-1.5 sm:gap-2">
                                <div className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full transition-all ${
                                  isSelected
                                    ? 'bg-emerald-400 shadow-lg shadow-emerald-400/50'
                                    : slot.is_available
                                      ? 'bg-gray-600 border border-gray-500'
                                      : 'bg-red-600'
                                }`} />

                                <span className="text-xs font-medium">
                                  {!slot.is_available
                                    ? 'Booked'
                                    : isSelected
                                      ? 'Selected'
                                      : isDisabledByRule
                                        ? 'Not consecutive'
                                        : 'Available'
                                  }
                                </span>
                              </div>

                              {slot.booking_type === 'capacity_based' && (
                                <div className="text-xs bg-blue-900/30 text-blue-300 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full">
                                  Capacity
                                </div>
                              )}
                            </div>

                            {/* Visual indicator for non-consecutive slots */}
                            {isDisabledByRule && selectedSlots.length > 0 && (
                              <div className="absolute top-2 right-2">
                                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
                              </div>
                            )}


                          </motion.button>
                        );
                      })}
                    </div>

                    {/* Legend */}
                    <div className="bg-gray-800/30 rounded-xl p-3 sm:p-4 border border-gray-700/50">
                      <h4 className="text-xs sm:text-sm font-medium text-gray-300 mb-2 sm:mb-3">Legend</h4>
                      <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs">
                        <div className="flex items-center gap-1.5 sm:gap-2">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-emerald-400 rounded-full" />
                          <span className="text-gray-300">Selected</span>
                        </div>
                        <div className="flex items-center gap-1.5 sm:gap-2">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-gray-600 border border-gray-500 rounded-full" />
                          <span className="text-gray-300">Available</span>
                        </div>
                        <div className="flex items-center gap-1.5 sm:gap-2">
                          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-red-600 rounded-full" />
                          <span className="text-gray-300">Booked</span>
                        </div>
                        {selectedSlots.length > 0 && availableTimeSlots.some(slot => slot.booking_type === 'court_based') && (
                          <div className="flex items-center gap-1.5 sm:gap-2">
                            <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-gray-600 rounded-full opacity-50" />
                            <span className="text-gray-300">Non-consecutive</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Selected Slots Summary */}
                {selectedSlots.length > 0 && (
                  <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-emerald-900/20 rounded-lg border border-emerald-800/30">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium text-emerald-300">Selected Slots</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedSlots([]);
                          setSelectedSlotPrices({});
                        }}
                        className="text-xs text-red-400 hover:text-red-300 transition-colors"
                      >
                        <X size={12} className="mr-1" />
                        Clear All
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {selectedSlots.map(slot => (
                        <span
                          key={slot}
                          className="bg-emerald-800/80 text-white px-3 py-1 rounded-full text-sm flex items-center gap-1"
                        >
                          {slot.split(' - ')[0]} <ChevronRight size={14} /> {slot.split(' - ')[1]}
                          <span className="font-semibold ml-1">₹{selectedSlotPrices[slot]?.toFixed(2)}</span>
                        </span>
                      ))}
                    </div>
                    <div className="pt-3 border-t border-emerald-800/30 flex justify-between">
                      <span className="font-medium text-emerald-300">Total:</span>
                      <span className="text-lg font-bold text-emerald-400">₹{calculateTotalPrice().toFixed(2)}</span>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {/* Step 3: Confirmation */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              variants={stepVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="space-y-4 sm:space-y-6"
            >
              {/* Booking Summary */}
              <div className="bg-gray-800 rounded-xl p-4 sm:p-6 border border-emerald-800/30">
                <h2 className="text-base sm:text-lg font-semibold mb-4 sm:mb-6 flex items-center gap-2">
                  <Check className="text-emerald-400" size={18} />
                  Confirm Booking
                </h2>

                {/* Booking Details */}
                <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 p-3 sm:p-4 bg-gray-700/50 rounded-lg">
                    <div>
                      <div className="text-xs sm:text-sm text-gray-400">Venue</div>
                      <div className="font-medium text-white text-sm sm:text-base">{venueDetails?.name}</div>
                    </div>
                    <div>
                      <div className="text-xs sm:text-sm text-gray-400">Sport</div>
                      <div className="font-medium text-white text-sm sm:text-base">{sportDetails?.name}</div>
                    </div>
                    <div>
                      <div className="text-xs sm:text-sm text-gray-400">Date</div>
                      <div className="font-medium text-white text-sm sm:text-base">
                        {localSelectedDate ? format(parseISO(localSelectedDate), 'EEEE, MMM dd, yyyy') : ''}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs sm:text-sm text-gray-400">Time</div>
                      <div className="font-medium text-white text-sm sm:text-base">
                        {selectedSlots.length > 0 && (
                          `${selectedSlots[0].split(' - ')[0]} - ${selectedSlots[selectedSlots.length - 1].split(' - ')[1]}`
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Selected Slots */}
                  <div className="p-3 sm:p-4 bg-emerald-900/20 rounded-lg border border-emerald-800/30">
                    <h4 className="font-medium text-emerald-300 mb-3">Selected Time Slots</h4>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {selectedSlots.map(slot => (
                        <span
                          key={slot}
                          className="bg-emerald-800/80 text-white px-3 py-1 rounded-full text-sm"
                        >
                          {slot} - ₹{selectedSlotPrices[slot]?.toFixed(2)}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Price Breakdown */}
                  <div className="p-3 sm:p-4 bg-gray-700/50 rounded-lg">
                    <h4 className="font-medium text-gray-300 mb-3">Price Breakdown</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Subtotal</span>
                        <span className="text-white">₹{calculateTotalPrice().toFixed(2)}</span>
                      </div>
                      {couponValidation?.valid && (
                        <div className="flex justify-between text-green-400">
                          <span>Discount ({couponCode})</span>
                          <span>-₹{getDiscountAmount().toFixed(2)}</span>
                        </div>
                      )}
                      <div className="border-t border-gray-600 pt-2 flex justify-between font-bold">
                        <span className="text-emerald-300">Total</span>
                        <span className="text-emerald-400 text-lg">₹{getFinalPrice().toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>



                {/* Coupon Code */}
                <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                  <h4 className="font-medium text-gray-300 text-sm sm:text-base">Coupon Code (Optional)</h4>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                      placeholder="Enter coupon code"
                      className="flex-1 p-2.5 sm:p-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-white text-sm sm:text-base min-h-[48px] touch-manipulation"
                      style={{ fontSize: '16px', WebkitTapHighlightColor: 'transparent' }}
                    />
                    <Button
                      onClick={() => validateCoupon(couponCode)}
                      disabled={!couponCode.trim() || couponLoading}
                      className="bg-emerald-600 hover:bg-emerald-700 text-white px-3 sm:px-4 py-2.5 sm:py-3 text-sm sm:text-base min-h-[44px]"
                    >
                      {couponLoading ? <Loader className="animate-spin" size={14} /> : 'Apply'}
                    </Button>
                  </div>
                  {couponValidation && (
                    <div className={`text-sm ${couponValidation.valid ? 'text-green-400' : 'text-red-400'}`}>
                      {couponValidation.valid
                        ? `Coupon applied! You saved ₹${couponValidation.discount_amount}`
                        : couponValidation.error
                      }
                    </div>
                  )}
                </div>

                {/* Payment Button */}
                <button
                  type="button"
                  onClick={handlePayment}
                  disabled={loading.booking || loading.payment || selectedSlots.length === 0}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 sm:py-3.5 text-base sm:text-lg font-semibold rounded-lg transition-all shadow-lg shadow-emerald-800/30 flex items-center justify-center gap-2 min-h-[48px]"
                >
                  {loading.booking || loading.payment ? (
                    <>
                      <Loader className="animate-spin" size={20} />
                      {loading.payment ? 'Initializing Payment...' : 'Processing Booking...'}
                    </>
                  ) : (
                    <>
                      <CreditCard size={20} />
                      Pay ₹{getFinalPrice().toFixed(2)} & Confirm Booking
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer Navigation */}
      <div className="sticky bottom-0 bg-gradient-to-t from-gray-900 via-gray-900/95 to-gray-900/90 backdrop-blur-sm border-t border-emerald-800/20 p-3 sm:p-4">
        <div className="container mx-auto max-w-4xl">
          <div className="flex justify-between items-center gap-3 sm:gap-4">
            <button
              type="button"
              onClick={handleBack}
              disabled={loading.booking || loading.payment}
              className="flex items-center gap-2 px-4 sm:px-6 py-2.5 sm:py-3 bg-gray-800 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-750 hover:border-gray-600 transition-all disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base min-h-[44px]"
            >
              <ChevronLeft size={14} />
              {currentStep === 1 ? 'Back' : 'Previous'}
            </button>

            {currentStep < 3 ? (
              <button
                type="button"
                onClick={handleNext}
                disabled={loading.booking || loading.payment || !canProceedToNextStep()}
                className="flex items-center gap-2 px-4 sm:px-6 py-2.5 sm:py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-emerald-800/30 text-sm sm:text-base min-h-[44px]"
              >
                Next
                <ChevronRight size={14} />
              </button>
            ) : (
              <button
                type="button"
                onClick={handlePayment}
                disabled={loading.payment || loading.booking || selectedSlots.length === 0}
                className="flex items-center gap-2 px-4 sm:px-6 py-2.5 sm:py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-emerald-800/30 text-sm sm:text-base min-h-[44px]"
              >
                {loading.payment || loading.booking ? (
                  <>
                    <Loader className="animate-spin" size={14} />
                    {loading.payment ? 'Payment...' : 'Booking...'}
                  </>
                ) : (
                  <>
                    <CreditCard size={14} />
                    Pay & Complete Booking
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Non-consecutive slot selection warning popup */}
      {showNonConsecutiveWarning && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-3 sm:p-4">
          <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 sm:p-6 max-w-md w-full mx-3 sm:mx-4 shadow-xl">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-yellow-900/30 rounded-full flex items-center justify-center">
                <AlertCircle className="w-5 h-5 text-yellow-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-base sm:text-lg font-semibold text-white mb-2">
                  Consecutive Slots Only
                </h3>
                <p className="text-gray-300 text-sm mb-3 sm:mb-4">
                  Please select consecutive time slots only. You cannot book slots with gaps between them.
                  This ensures optimal court utilization and prevents scheduling conflicts.
                </p>
                <button
                  onClick={() => setShowNonConsecutiveWarning(false)}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-2.5 sm:py-3 px-4 rounded-lg transition-colors font-medium text-sm sm:text-base min-h-[44px]"
                >
                  Got it
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Booking Success Modal with Calendar Integration */}
      <BookingSuccessModal
        isOpen={showBookingSuccess}
        onClose={() => setShowBookingSuccess(false)}
        bookingDetails={bookingSuccessDetails}
        onContinue={() => {
          setShowBookingSuccess(false);
          navigate('/bookings');
        }}
      />
    </motion.div>
  );
};

export default BookingPage;
