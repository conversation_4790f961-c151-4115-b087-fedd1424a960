import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { AdminBookingInfo, Booking, BookingStatus } from '@/types/help';
import BookingsList from '@/components/admin/BookingsList';
import { protectedBookingUpdate } from '@/utils/protectedSupabase';
import { logSecurityEvent } from '@/utils/adminSecurity';
import { Search, X, ArrowLeft, RefreshCw, Filter, ChevronDown, ChevronUp, Calendar, Settings, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import VenueSelector from '@/components/admin/VenueSelector';

const Bookings_Mobile: React.FC = () => {
  const { userRole, user } = useAuth();
  const navigate = useNavigate();
  const [adminVenues, setAdminVenues] = useState<{ venue_id: string }[]>([]);
  const [venues, setVenues] = useState<{ id: string; name: string }[]>([]);
  const [selectedVenueId, setSelectedVenueId] = useState<string>('all');
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | BookingStatus>('all');
  const [paymentFilter, setPaymentFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<'all' | 'cash' | 'online' | 'card' | 'free'>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filtersExpanded, setFiltersExpanded] = useState<boolean>(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const fetchAdminVenues = async () => {
      if (userRole === 'admin') {
        const { data, error } = await supabase.rpc('get_admin_venues');
        if (!error) {
          setAdminVenues(data || []);

          // Fetch venue details for the selector
          if (data && data.length > 0) {
            const venueIds = data.map((v: any) => v.venue_id);
            const { data: venueDetails, error: venueError } = await supabase
              .from('venues')
              .select('id, name')
              .in('id', venueIds)
              .eq('is_active', true)
              .order('name');

            if (!venueError) {
              setVenues(venueDetails || []);
              // Set default to first venue if only one venue
              if (venueDetails && venueDetails.length === 1) {
                setSelectedVenueId(venueDetails[0].id);
              }
            }
          }
        }
      } else if (userRole === 'super_admin') {
        setAdminVenues([]); // super_admin can see all venues

        // Fetch all venues for super admin
        const { data: allVenues, error } = await supabase
          .from('venues')
          .select('id, name')
          .eq('is_active', true)
          .order('name');

        if (!error) {
          setVenues(allVenues || []);
        }
      }
    };
    fetchAdminVenues();
  }, [userRole]);

  const fetchBookings = useCallback(async () => {
    setLoading(true);
    try {
      // Fetching bookings with security and performance optimization - details removed for production security

      // ✅ PERFORMANCE OPTIMIZED: Single query with INNER joins for venue filtering
      // ✅ CACHE BUSTING: Add timestamp to force fresh data
      const cacheKey = Date.now();
      // Cache-busting timestamp logging removed for production security

      // ✅ FORCE FRESH DATA: Create new supabase client instance to bypass any caching
      let query = supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          total_price,
          status,
          booking_reference,
          payment_reference,
          payment_status,
          payment_method,
          user_id,
          guest_name,
          guest_phone,
          created_at,
          booked_by_admin_id,
          cancellation_reason,
          court:courts!inner (
            id,
            name,
            venue_id,
            venue:venues!inner (
              id,
              name
            ),
            sport:sports (
              id,
              name
            )
          ),
          admin_booking:admin_bookings(
            id,
            booking_id,
            admin_id,
            customer_name,
            customer_phone,
            payment_method,
            payment_status,
            amount_collected,
            created_at,
            notes
          )
        `)
        .order('created_at', { ascending: false })
        .order('booking_date', { ascending: false });

      // ✅ SECURITY & PERFORMANCE: Apply venue filtering with joins for admin users
      if (userRole === 'admin' && adminVenues.length > 0) {
        const venueIds = adminVenues.map(v => v.venue_id);
        // Use the joined venue table for filtering - single query, no round trips
        query = query.in('court.venue.id', venueIds);
      }

      // Apply venue-specific filtering if a specific venue is selected
      if (selectedVenueId && selectedVenueId !== 'all') {
        query = query.eq('court.venue.id', selectedVenueId);
      }

      // Apply other filters
      if (filter !== 'all') {
        query = query.eq('status', filter);
      }
      if (paymentFilter !== 'all') {
        query = query.eq('payment_status', paymentFilter);
      }
      if (paymentMethodFilter !== 'all') {
        query = query.eq('payment_method', paymentMethodFilter);
      }

      // Add search functionality for booking reference
      if (searchQuery.trim()) {
        const trimmedQuery = searchQuery.trim().toUpperCase();
        // Search by booking reference (exact match or partial)
        if (trimmedQuery.startsWith('GR2P-')) {
          query = query.ilike('booking_reference', `%${trimmedQuery}%`);
        } else {
          // If user types without GR2P- prefix, add it
          query = query.ilike('booking_reference', `%GR2P-${trimmedQuery}%`);
        }
      }

      const { data, error } = await query;
      if (error) throw error;

      // ✅ PERFORMANCE OPTIMIZED: Batch profile queries instead of N+1
      if (data && data.length > 0) {
        // Get unique user IDs and admin IDs
        const userIds = [...new Set(data.map(b => b.user_id).filter(Boolean))];
        const adminIds = [...new Set(data.map(b => b.booked_by_admin_id).filter(Boolean))];

        // ✅ PRIVACY COMPLIANT: Batch fetch user profiles (name + phone, NO email)
        const userProfilesPromise = userIds.length > 0
          ? supabase.from('profiles').select('id, full_name, phone').in('id', userIds)
          : Promise.resolve({ data: [], error: null });

        // ✅ PRIVACY COMPLIANT: Batch fetch admin profiles (name only, NO email)
        const adminProfilesPromise = adminIds.length > 0
          ? supabase.from('profiles').select('id, full_name').in('id', adminIds)
          : Promise.resolve({ data: [], error: null });

        // Execute batch queries in parallel
        const [userProfilesResult, adminProfilesResult] = await Promise.all([
          userProfilesPromise,
          adminProfilesPromise
        ]);

        // Create lookup maps for O(1) access
        const userProfileMap = new Map();
        const adminProfileMap = new Map();

        userProfilesResult.data?.forEach(profile => {
          userProfileMap.set(profile.id, profile);
        });

        adminProfilesResult.data?.forEach(profile => {
          adminProfileMap.set(profile.id, profile);
        });

        // Process bookings with profile data
        const processedBookings = data.map(booking => {
          const processedBooking: Booking = {
            ...booking,
            admin_booking: null,
            court: booking.court,
            status: booking.status as BookingStatus,
          };

          // Handle admin_booking - if it's an array with elements, take the first one
          if (booking.admin_booking && Array.isArray(booking.admin_booking) && booking.admin_booking.length > 0) {
            processedBooking.admin_booking = booking.admin_booking[0] as AdminBookingInfo;
          }

          // ✅ PRIVACY COMPLIANT: Add user profile data from lookup map (name + phone, NO email)
          if (booking.user_id && userProfileMap.has(booking.user_id)) {
            const userProfile = userProfileMap.get(booking.user_id);
            processedBooking.user_info = {
              full_name: userProfile.full_name,
              phone: userProfile.phone,
              email: null // ✅ EMAIL EXCLUDED for admin privacy compliance
            };
          }

          // ✅ PRIVACY COMPLIANT: Add admin profile data from lookup map (name only, NO email)
          if (booking.booked_by_admin_id && adminProfileMap.has(booking.booked_by_admin_id)) {
            const adminProfile = adminProfileMap.get(booking.booked_by_admin_id);
            processedBooking.admin_info = {
              full_name: adminProfile.full_name,
              email: null // ✅ EMAIL EXCLUDED for privacy
            };
          }

          return processedBooking;
        });

        // Booking sort order verification logging removed for production security

        setBookings(processedBookings);
      } else {
        setBookings([]);
      }
    } catch (error) {
      // Error fetching bookings - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to load bookings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [filter, paymentFilter, paymentMethodFilter, searchQuery, userRole, adminVenues, selectedVenueId, refreshKey]);

  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  const updateBookingStatus = async (bookingId: string, status: BookingStatus, cancellationReason?: string) => {
    try {
      // Security check: Only allow cancellation and completion
      if (status !== 'cancelled' && status !== 'completed') {
        throw new Error('Invalid status change. Only cancellation and completion are allowed.');
      }

      if (!user?.id) {
        throw new Error('User authentication required');
      }

      // ✅ SECURITY ENHANCEMENT: Use protected booking update with role validation
      await protectedBookingUpdate(
        bookingId,
        status,
        {
          userId: user.id,
          fallbackToUnsafe: true, // Maintain backwards compatibility
          logOnly: true, // Only log security events for now
        },
        cancellationReason
      );

      // Log the security-validated operation
      logSecurityEvent('MOBILE_BOOKING_STATUS_UPDATE', user.id, {
        bookingId,
        status,
        cancellationReason,
        userRole
      });

      toast({
        title: 'Status Updated',
        description: status === 'cancelled'
          ? `Booking has been cancelled. Reason: ${cancellationReason}`
          : `Booking has been marked as ${status}`,
      });

      fetchBookings();
    } catch (error) {
      console.error('Error updating booking:', error);
      toast({
        title: 'Error',
        description: 'Failed to update booking status',
        variant: 'destructive',
      });
      throw error; // Re-throw to handle in the modal
    }
  };

  return (
    <div className="admin-mobile-page min-h-screen bg-gradient-to-b from-emerald-900/20 to-black relative overflow-hidden pb-16">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1485395037613-e83d5c1f5290?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')] opacity-5 bg-center bg-cover"></div>

      {/* Header - Mobile-First Design */}
      <div className="sticky top-0 z-10 bg-gradient-to-r from-emerald-600 to-emerald-800 shadow-xl border-b border-emerald-400/30">
        <div className="flex items-center justify-between px-4 py-4">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/mobile-home')}
              className="mr-3 p-2 rounded-full hover:bg-white/10 transition-all duration-300 min-h-[48px] min-w-[48px] flex items-center justify-center"
            >
              <ArrowLeft className="h-6 w-6 text-white" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white tracking-wide">Booking Management</h1>
              <p className="text-emerald-200 text-sm opacity-90">Manage all venue bookings</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Manual refresh triggered - details removed for production security
              setRefreshKey(prev => prev + 1);
              toast({
                title: 'Refreshing Data',
                description: 'Fetching latest bookings with newest-first sort order...',
              });
              fetchBookings();
            }}
            disabled={loading}
            className="text-white hover:bg-white/10 min-h-[48px] min-w-[48px] rounded-full"
            title="Refresh bookings data"
          >
            <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Main Content - Mobile-First Container */}
      <div className="container mx-auto px-4 py-6 space-y-6 relative z-10">

        {/* Venue Selector - Mobile-Optimized */}
        {venues.length > 0 && (
          <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-emerald-900/20 hover:border-emerald-700/50 backdrop-blur-sm transition-all duration-300">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-emerald-500/20 rounded-xl flex items-center justify-center mr-4 border border-emerald-500/30">
                  <Settings className="w-6 h-6 text-emerald-400" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white mb-1">Venue Selection</h3>
                  <p className="text-sm text-gray-300 opacity-90">Choose venue to manage</p>
                </div>
              </div>
              <VenueSelector
                venues={venues}
                selectedVenueId={selectedVenueId}
                onVenueChange={setSelectedVenueId}
                userRole={userRole}
                variant="mobile"
                label="Filter by Venue"
                showAllOption={userRole === 'super_admin' || venues.length > 1}
              />
            </div>
          </div>
        )}

        {/* Search Section - Mobile-Optimized */}
        <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-blue-900/20 hover:border-blue-700/50 backdrop-blur-sm transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4 border border-blue-500/30">
                <Search className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white mb-1">Search Bookings</h3>
                <p className="text-sm text-gray-300 opacity-90">Find by booking reference</p>
              </div>
            </div>
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Enter booking reference (e.g., GR2P-1431D93D or 1431D93D)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-12 py-4 bg-slate-800/50 border border-blue-500/30 rounded-xl text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all min-h-[48px]"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-gray-200 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Collapsible Filters Section - Mobile-Optimized */}
        <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-purple-900/20 hover:border-purple-700/50 backdrop-blur-sm transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4 border border-purple-500/30">
                  <Filter className="w-6 h-6 text-purple-400" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white mb-1">Advanced Filters</h3>
                  <p className="text-sm text-gray-300 opacity-90">Filter bookings by status & payment</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setFiltersExpanded(!filtersExpanded)}
                className="text-white hover:bg-white/10 min-h-[48px] min-w-[48px] rounded-full"
              >
                {filtersExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
              </Button>
            </div>

            {filtersExpanded && (
              <div className="space-y-6 animate-in slide-in-from-top-2 duration-300">
                {/* Booking Status Filters */}
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Booking Status</h4>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setFilter('all')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        filter === 'all'
                          ? 'bg-emerald-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      All
                    </button>
                    <button
                      onClick={() => setFilter('confirmed')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        filter === 'confirmed'
                          ? 'bg-green-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Confirmed
                    </button>
                    <button
                      onClick={() => setFilter('cancelled')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        filter === 'cancelled'
                          ? 'bg-red-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Cancelled
                    </button>
                    <button
                      onClick={() => setFilter('completed')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        filter === 'completed'
                          ? 'bg-blue-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Completed
                    </button>
                    <button
                      onClick={() => setFilter('pending')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        filter === 'pending'
                          ? 'bg-yellow-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Pending
                    </button>
                  </div>
                </div>

                {/* Payment Status Filters */}
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Payment Status</h4>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setPaymentFilter('all')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentFilter === 'all'
                          ? 'bg-emerald-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      All Payments
                    </button>
                    <button
                      onClick={() => setPaymentFilter('completed')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentFilter === 'completed'
                          ? 'bg-green-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Completed
                    </button>
                    <button
                      onClick={() => setPaymentFilter('pending')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentFilter === 'pending'
                          ? 'bg-yellow-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Pending
                    </button>
                    <button
                      onClick={() => setPaymentFilter('failed')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentFilter === 'failed'
                          ? 'bg-red-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Failed
                    </button>
                  </div>
                </div>
                {/* Payment Method Filters */}
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Payment Method</h4>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setPaymentMethodFilter('all')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentMethodFilter === 'all'
                          ? 'bg-emerald-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      All Methods
                    </button>
                    <button
                      onClick={() => setPaymentMethodFilter('cash')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentMethodFilter === 'cash'
                          ? 'bg-green-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Cash
                    </button>
                    <button
                      onClick={() => setPaymentMethodFilter('online')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentMethodFilter === 'online'
                          ? 'bg-blue-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Online
                    </button>
                    <button
                      onClick={() => setPaymentMethodFilter('card')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentMethodFilter === 'card'
                          ? 'bg-purple-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Card
                    </button>
                    <button
                      onClick={() => setPaymentMethodFilter('free')}
                      className={`px-4 py-2 text-sm rounded-xl transition-all min-h-[44px] ${
                        paymentMethodFilter === 'free'
                          ? 'bg-gray-500 text-white shadow-lg'
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      Free
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        {/* Bookings List Section - Mobile-Optimized */}
        <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-gray-900/20 hover:border-gray-700/50 backdrop-blur-sm transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-indigo-500/20 rounded-xl flex items-center justify-center mr-4 border border-indigo-500/30">
                <Calendar className="w-6 h-6 text-indigo-400" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white mb-1">Booking Records</h3>
                <p className="text-sm text-gray-300 opacity-90">Manage venue bookings</p>
              </div>
            </div>
            <BookingsList
              bookings={bookings}
              isLoading={loading}
              onStatusUpdate={updateBookingStatus}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Bookings_Mobile; 
