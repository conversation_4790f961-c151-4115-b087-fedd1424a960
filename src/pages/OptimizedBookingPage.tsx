import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Check, CreditCard, Loader } from 'lucide-react';

// Import optimized components
import MobileConsolidatedBooking from '@/components/booking/MobileConsolidatedBooking';
import ConsolidatedBookingInterface from '@/components/booking/ConsolidatedBookingInterface';
import BookingSuccessModal from '@/components/BookingSuccessModal';

// Import hooks and utilities
import { useIsMobile } from '@/hooks/use-mobile';
import { useAvailabilityCache } from '@/utils/availabilityCache';
import {
  sanitizeBookingInput,
  sanitizePhoneNumber,
  generateCSRFToken,
  validateBookingData,
  type SecureBookingData
} from '@/utils/bookingSecurity';

// Import existing payment and booking logic
import { supabase } from '@/integrations/supabase/client';

// Declare Razorpay global
declare global {
  interface Window {
    Razorpay: any;
  }
}

interface BookingSelection {
  venue: string;
  sport: string;
  court: string;
  date: string;
  slots: string[];
  prices: Record<string, number>;
}

const OptimizedBookingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { getCacheStats } = useAvailabilityCache();

  // URL parameters
  const initialVenueId = searchParams.get('venue');
  const initialSportId = searchParams.get('sport');
  const initialCourtId = searchParams.get('court');
  const initialDate = searchParams.get('date');

  // State management
  const [currentStep, setCurrentStep] = useState(1); // 1: Selection, 2: Payment
  const [bookingSelection, setBookingSelection] = useState<BookingSelection | null>(null);
  const [loading, setLoading] = useState({
    payment: false,
    booking: false
  });

  // Guest details (for Step 2)
  const [guestDetails, setGuestDetails] = useState({
    name: '',
    phone: '',
    couponCode: ''
  });

  // Booking success state
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [bookingReference, setBookingReference] = useState('');

  // Security
  const [csrfToken] = useState(() => generateCSRFToken());
  const [authVerified, setAuthVerified] = useState(false);

  // Verify authentication on mount
  useEffect(() => {
    if (user) {
      setAuthVerified(true);
    } else {
      navigate('/login');
    }
  }, [user, navigate]);

  // Handle selection completion from Step 1
  const handleSelectionComplete = useCallback((selection: BookingSelection) => {
    setBookingSelection(selection);
    setCurrentStep(2);
  }, []);

  // Calculate total price
  const calculateTotalPrice = useCallback(() => {
    if (!bookingSelection) return 0;
    return Object.values(bookingSelection.prices).reduce((sum, price) => sum + price, 0);
  }, [bookingSelection]);

  // Handle payment processing (preserved from original BookingPage.tsx)
  const handlePayment = useCallback(async () => {
    if (!authVerified || !user || !bookingSelection) {
      toast({
        title: "Missing information",
        description: "Please complete all booking details",
        variant: "destructive",
      });
      return;
    }

    setLoading(prev => ({ ...prev, payment: true }));

    try {
      // Validate booking data
      const secureBookingData: SecureBookingData = {
        courtId: bookingSelection.court,
        date: bookingSelection.date,
        slots: bookingSelection.slots,
        guestName: sanitizeBookingInput(guestDetails.name),
        guestPhone: sanitizePhoneNumber(guestDetails.phone),
        couponCode: guestDetails.couponCode,
        csrfToken: csrfToken
      };

      const validation = validateBookingData(secureBookingData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Create Razorpay order
      const totalAmount = calculateTotalPrice();
      const receipt = `booking_${Date.now()}_${user.id.substring(0, 8)}`;

      const response = await supabase.functions.invoke('create-razorpay-order', {
        body: {
          amount: totalAmount * 100, // Convert to paise
          receipt: receipt,
          notes: {
            court_id: bookingSelection.court,
            date: bookingSelection.date,
            slots: bookingSelection.slots.join(', '),
            venueId: bookingSelection.venue,
            sportId: bookingSelection.sport,
            user_id: user.id,
            guest_name: guestDetails.name,
            guest_phone: guestDetails.phone
          },
          courtId: bookingSelection.court,
          date: bookingSelection.date,
          couponCode: guestDetails.couponCode,
          guestName: guestDetails.name,
          guestPhone: guestDetails.phone,
          csrfToken: csrfToken
        }
      });

      if (response.error) {
        throw new Error('Payment order creation failed');
      }

      const { order, key_id } = response.data;

      // Initialize Razorpay
      const options = {
        key: key_id,
        amount: order.amount,
        currency: order.currency,
        name: "Grid२Play",
        description: `Court Booking for ${bookingSelection.date}`,
        order_id: order.id,
        prefill: {
          name: sanitizeBookingInput(guestDetails.name || user.email || ''),
          email: user.email,
          contact: sanitizePhoneNumber(guestDetails.phone || '')
        },
        theme: {
          color: "#047857" // Emerald-800
        },
        handler: async (response: any) => {
          await handleBookingCreation(response.razorpay_payment_id, order.id, response.razorpay_signature);
        },
        modal: {
          ondismiss: () => {
            setLoading(prev => ({ ...prev, payment: false }));
          }
        }
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Payment initialization error:', error);
      toast({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to initialize payment",
        variant: "destructive",
      });
      setLoading(prev => ({ ...prev, payment: false }));
    }
  }, [authVerified, user, bookingSelection, guestDetails, csrfToken, calculateTotalPrice, toast]);

  // Handle booking creation after successful payment
  const handleBookingCreation = useCallback(async (
    paymentId: string,
    orderId: string,
    signature: string
  ) => {
    if (!bookingSelection || !user) return;

    setLoading(prev => ({ ...prev, booking: true }));

    try {
      // Create booking using existing function
      const startTime = bookingSelection.slots[0].split(' - ')[0];
      const endTime = bookingSelection.slots[bookingSelection.slots.length - 1].split(' - ')[1];
      const originalPrice = calculateTotalPrice();

      const { data, error } = await supabase.rpc('create_booking_with_coupon', {
        p_court_id: bookingSelection.court,
        p_user_id: user.id,
        p_booking_date: bookingSelection.date,
        p_start_time: startTime,
        p_end_time: endTime,
        p_original_price: originalPrice,
        p_coupon_code: guestDetails.couponCode || null,
        p_guest_name: guestDetails.name || null,
        p_guest_phone: guestDetails.phone || null,
        p_payment_reference: paymentId,
        p_payment_status: 'completed',
        p_payment_method: 'online'
      });

      if (error) throw error;

      // Success
      setBookingReference(data.booking_reference);
      setShowSuccessModal(true);
      
      toast({
        title: "Booking Confirmed!",
        description: `Your booking has been confirmed. Reference: ${data.booking_reference}`,
      });

    } catch (error) {
      console.error('Booking creation error:', error);
      toast({
        title: "Booking Failed",
        description: error instanceof Error ? error.message : "Failed to create booking",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, booking: false, payment: false }));
    }
  }, [bookingSelection, user, guestDetails, calculateTotalPrice, toast]);

  // Step animations
  const stepVariants = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  };

  if (!authVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Loader className="w-8 h-8 animate-spin text-emerald-400" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <button
              onClick={() => currentStep === 1 ? navigate(-1) : setCurrentStep(1)}
              className="p-2 hover:bg-gray-800 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
            >
              <ArrowLeft className="w-5 h-5 text-white" />
            </button>
            <h1 className="text-lg font-bold text-white">
              {currentStep === 1 ? 'Quick Booking' : 'Confirm & Pay'}
            </h1>
          </div>
          
          {/* Step indicator */}
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= 1 ? 'bg-emerald-600 text-white' : 'bg-gray-700 text-gray-400'
            }`}>
              {currentStep > 1 ? <Check size={16} /> : '1'}
            </div>
            <div className={`w-8 h-1 ${currentStep >= 2 ? 'bg-emerald-600' : 'bg-gray-700'}`} />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= 2 ? 'bg-emerald-600 text-white' : 'bg-gray-700 text-gray-400'
            }`}>
              2
            </div>
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {(loading.booking || loading.payment) && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-gray-900 p-6 rounded-xl border border-emerald-500/20 text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto mb-4"
            />
            <p className="text-white font-medium">
              {loading.payment ? 'Initializing Payment...' : 'Processing Booking...'}
            </p>
          </div>
        </div>
      )}

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentStep === 1 && (
          <motion.div
            key="step1"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {isMobile ? (
              <MobileConsolidatedBooking
                onSelectionComplete={handleSelectionComplete}
                initialVenueId={initialVenueId || undefined}
                initialSportId={initialSportId || undefined}
              />
            ) : (
              <div className="container mx-auto px-4 py-6 max-w-6xl">
                <ConsolidatedBookingInterface
                  onSlotSelection={() => {}} // Not needed for desktop
                  onSelectionComplete={handleSelectionComplete}
                  initialVenueId={initialVenueId || undefined}
                  initialSportId={initialSportId || undefined}
                  initialCourtId={initialCourtId || undefined}
                  initialDate={initialDate || undefined}
                />
              </div>
            )}
          </motion.div>
        )}

        {currentStep === 2 && bookingSelection && (
          <motion.div
            key="step2"
            variants={stepVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="container mx-auto px-4 py-6 max-w-2xl"
          >
            {/* Step 2: Payment confirmation - preserved from original */}
            <div className="space-y-6">
              {/* Booking Summary */}
              <div className="bg-gray-800 rounded-xl p-6 border border-emerald-800/30">
                <h3 className="text-lg font-semibold text-white mb-4">Booking Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Date:</span>
                    <span className="text-white">{bookingSelection.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Slots:</span>
                    <span className="text-white">{bookingSelection.slots.join(', ')}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg pt-2 border-t border-gray-700">
                    <span className="text-gray-300">Total:</span>
                    <span className="text-emerald-400">₹{calculateTotalPrice()}</span>
                  </div>
                </div>
              </div>

              {/* Guest Details Form */}
              <div className="bg-gray-800 rounded-xl p-6 border border-emerald-800/30">
                <h3 className="text-lg font-semibold text-white mb-4">Guest Details</h3>
                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Full Name"
                    value={guestDetails.name}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                  <input
                    type="tel"
                    placeholder="Phone Number"
                    value={guestDetails.phone}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                  <input
                    type="text"
                    placeholder="Coupon Code (Optional)"
                    value={guestDetails.couponCode}
                    onChange={(e) => setGuestDetails(prev => ({ ...prev, couponCode: e.target.value }))}
                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white min-h-[48px]"
                  />
                </div>
              </div>

              {/* Payment Button */}
              <button
                onClick={handlePayment}
                disabled={loading.booking || loading.payment}
                className="w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-600 text-white py-4 text-lg font-semibold rounded-lg transition-all min-h-[48px] flex items-center justify-center gap-2"
              >
                {loading.payment || loading.booking ? (
                  <Loader className="animate-spin" size={20} />
                ) : (
                  <>
                    <CreditCard size={20} />
                    Pay ₹{calculateTotalPrice()} & Confirm Booking
                  </>
                )}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success Modal */}
      <BookingSuccessModal
        isOpen={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          navigate('/profile');
        }}
        bookingReference={bookingReference}
      />
    </div>
  );
};

export default OptimizedBookingPage;
