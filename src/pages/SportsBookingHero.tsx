import React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect, useCallback } from "react"
import { Button } from "@/components/ui/button"
import { ArrowRight, MapPin, Loader2, ChevronDown } from "lucide-react"
import { useNavigate } from "react-router-dom"

import { googleMapsService } from '@/utils/google-maps'
import { useIsMobile } from '@/hooks/use-mobile'

import { Address } from '@/types/location'

// Utility function
function cn(...inputs: (string | undefined)[]) {
  return inputs.filter(Boolean).join(' ')
}

// Sports figure SVG components
const SportsFigure1 = () => (
  <motion.svg
    width="80"
    height="80"
    viewBox="0 0 100 100"
    className="absolute text-emerald-900/20"
    initial={{ opacity: 0, scale: 0 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ delay: 2, duration: 1 }}
  >
    <motion.g
      animate={{ rotate: [0, 10, -10, 0] }}
      transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
    >
      {/* Head */}
      <circle cx="50" cy="20" r="8" fill="currentColor" />
      {/* Body */}
      <line x1="50" y1="28" x2="50" y2="60" stroke="currentColor" strokeWidth="3" />
      {/* Arms - basketball shooting pose */}
      <line x1="50" y1="35" x2="35" y2="25" stroke="currentColor" strokeWidth="3" />
      <line x1="50" y1="35" x2="65" y2="30" stroke="currentColor" strokeWidth="3" />
      {/* Legs */}
      <line x1="50" y1="60" x2="40" y2="80" stroke="currentColor" strokeWidth="3" />
      <line x1="50" y1="60" x2="60" y2="80" stroke="currentColor" strokeWidth="3" />
      {/* Basketball */}
      <circle cx="70" cy="25" r="4" fill="currentColor" opacity="0.7" />
    </motion.g>
  </motion.svg>
)

const SportsFigure2 = () => (
  <motion.svg
    width="80"
    height="80"
    viewBox="0 0 100 100"
    className="absolute text-emerald-900/20"
    initial={{ opacity: 0, scale: 0 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ delay: 2.5, duration: 1 }}
  >
    <motion.g
      animate={{ rotate: [0, -5, 5, 0] }}
      transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut" }}
    >
      {/* Head */}
      <circle cx="50" cy="20" r="8" fill="currentColor" />
      {/* Body */}
      <line x1="50" y1="28" x2="50" y2="60" stroke="currentColor" strokeWidth="3" />
      {/* Arms - tennis serve */}
      <line x1="50" y1="35" x2="30" y2="20" stroke="currentColor" strokeWidth="3" />
      <line x1="50" y1="35" x2="70" y2="45" stroke="currentColor" strokeWidth="3" />
      {/* Legs - running */}
      <line x1="50" y1="60" x2="35" y2="85" stroke="currentColor" strokeWidth="3" />
      <line x1="50" y1="60" x2="65" y2="75" stroke="currentColor" strokeWidth="3" />
      {/* Tennis racket */}
      <ellipse cx="75" cy="40" rx="3" ry="6" fill="none" stroke="currentColor" strokeWidth="2" />
    </motion.g>
  </motion.svg>
)

const SportsFigure3 = () => (
  <motion.svg
    width="80"
    height="80"
    viewBox="0 0 100 100"
    className="absolute text-emerald-900/20"
    initial={{ opacity: 0, scale: 0 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ delay: 3, duration: 1 }}
  >
    <motion.g
      animate={{ rotate: [0, 8, -8, 0] }}
      transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
    >
      {/* Head */}
      <circle cx="50" cy="20" r="8" fill="currentColor" />
      {/* Body */}
      <line x1="50" y1="28" x2="50" y2="60" stroke="currentColor" strokeWidth="3" />
      {/* Arms - football throw */}
      <line x1="50" y1="35" x2="25" y2="30" stroke="currentColor" strokeWidth="3" />
      <line x1="50" y1="35" x2="75" y2="20" stroke="currentColor" strokeWidth="3" />
      {/* Legs */}
      <line x1="50" y1="60" x2="45" y2="80" stroke="currentColor" strokeWidth="3" />
      <line x1="50" y1="60" x2="55" y2="80" stroke="currentColor" strokeWidth="3" />
      {/* Football */}
      <ellipse cx="80" cy="15" rx="3" ry="5" fill="currentColor" opacity="0.7" />
    </motion.g>
  </motion.svg>
)

// Text rotator component
interface TextRotatorProps {
  words: string[]
  className?: string
  interval?: number
}

const TextRotator = ({ words, className = "", interval = 2000 }: TextRotatorProps) => {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length)
    }, interval)

    return () => clearInterval(timer)
  }, [words.length, interval])

  return (
    <span className={cn("relative inline-block min-w-[15ch] text-center", className)}>
      <AnimatePresence mode="wait">
        <motion.span
          key={currentIndex}
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0, y: 20, filter: "blur(5px)" }}
          animate={{ opacity: 1, y: 0, filter: "blur(0px)" }}
          exit={{ opacity: 0, y: -20, filter: "blur(5px)" }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        >
          <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent font-black">
            {words[currentIndex]}
          </span>
        </motion.span>
      </AnimatePresence>
      <span className="opacity-0 text-center">{words[0]}</span>
    </span>
  )
}

// Main hero component
const SportsBookingHero = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const rotatingWords = ["Tournament!", "Championship!", "Match!", "Game on!", "Victory!"]



  // Location search state
  const [locationQuery, setLocationQuery] = useState('');
  const [locationSuggestions, setLocationSuggestions] = useState<Address[]>([]);
  const [isLocationSearching, setIsLocationSearching] = useState(false);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [useGoogleMaps, setUseGoogleMaps] = useState(true);

  // Enhanced location search with Google Maps Places Autocomplete and fallback
  const handleLocationSearch = useCallback(async (query: string) => {
    setLocationQuery(query);

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (query.length < 2) {
      setLocationSuggestions([]);
      setShowLocationSuggestions(false);
      return;
    }

    // Debounce search requests
    const timeout = setTimeout(async () => {
      setIsLocationSearching(true);

      try {
        let suggestions: Address[] = [];

        // Try Google Maps first if enabled
        if (useGoogleMaps) {
          try {
            suggestions = await googleMapsService.searchAddresses(query);

            // Search results logging removed for production security
          } catch (error) {
            // Google Maps search failed - disable for this session
            setUseGoogleMaps(false);
          }
        }

        // No location suggestions logging removed for production security

        setLocationSuggestions(suggestions);
        setShowLocationSuggestions(suggestions.length > 0);
      } catch (error) {
        // Location search failed - error details removed for production security
        setLocationSuggestions([]);
        setShowLocationSuggestions(false);
      } finally {
        setIsLocationSearching(false);
      }
    }, 300); // 300ms debounce

    setSearchTimeout(timeout);
  }, [searchTimeout, useGoogleMaps]);

  // Handle location selection with coordinates
  const handleLocationSelect = async (suggestion: Address) => {
    // Show the specific area/locality in the input field for better UX
    setLocationQuery(suggestion.area || suggestion.city || suggestion.display_name || '');
    setLocationSuggestions([]);
    setShowLocationSuggestions(false);

    // Get coordinates for the selected location
    let coordinates: { lat: number; lng: number } | null = null;

    try {
      // First, try to use coordinates from Google Maps result
      if (suggestion.coordinates) {
        coordinates = {
          lat: suggestion.coordinates.latitude,
          lng: suggestion.coordinates.longitude
        };

        // GPS coordinates logging removed for production security
      } else {
        // No coordinates available - location will work without coordinates
      }
    } catch (error) {
      // Failed to get coordinates for selected location - error details removed for production security
    }

    // Build navigation URL with location and coordinates
    // Use the full display name that the user actually selected, not just area/city
    const locationName = suggestion.display_name || suggestion.area || suggestion.city || '';
    let navigationUrl = `/venues?location=${encodeURIComponent(locationName)}`;

    if (coordinates) {
      navigationUrl += `&lat=${coordinates.lat}&lng=${coordinates.lng}`;
    }

    // Navigation with location data logging removed for production security

    // Navigate to venues with location filter and coordinates
    navigate(navigationUrl);
  };

  // Initialize component and preload Google Maps
  useEffect(() => {
    // Preload Google Maps API for better performance
    if (useGoogleMaps) {
      googleMapsService.loadGoogleMapsAPI().catch(() => {
        // Failed to preload Google Maps API - error details removed for production security
        setUseGoogleMaps(false);
      });
    }

    // Cleanup function
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout, useGoogleMaps]);

  // Close location suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.location-search-container')) {
        setShowLocationSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);



  return (
    <section className="relative min-h-screen w-full overflow-hidden bg-black flex flex-col justify-center pt-32 md:pt-40">
      {/* Background Photo */}
      <div className="absolute inset-0 z-0">
        <img
          src="https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/vedios//Google_AI_Studio_2025-07-18T23_03_43.172Z.png"
          alt="Sports Hero Background"
          className="w-full h-full object-cover"
          loading="lazy"
          decoding="async"
        />
      </div>

      {/* Overlay for text readability */}
      <div className="absolute inset-0 bg-black/50 z-[1]"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-emerald-900/30 via-emerald-800/20 to-emerald-900/30 z-[1]"></div>

      {/* Gradient background */}
      <div className="absolute inset-0 z-[2]">
        <div className="absolute inset-0 bg-gradient-to-br from-black/20 via-gray-900/30 to-emerald-900/40" />
        
        {/* Animated gradient orbs - Conditionally rendered for performance */}
        {!isMobile && (
          <>
            <motion.div
              className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-emerald-900/15 blur-3xl hero-animations z-[3]"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.2, 0.4, 0.2],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            <motion.div
              className="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full bg-emerald-800/15 blur-3xl hero-animations z-[3]"
              animate={{
                scale: [1.2, 1, 1.2],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
          </>
        )}

        {/* Mobile-optimized static gradient orbs */}
        {isMobile && (
          <>
            <div className="absolute top-1/4 left-1/4 w-48 h-48 rounded-full bg-emerald-900/10 blur-xl z-[3]" />
            <div className="absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full bg-emerald-800/10 blur-xl z-[3]" />
          </>
        )}

        {/* Grid pattern - Optimized for mobile */}
        <div
          className="absolute inset-0 opacity-5 z-[4]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.4) 1px, transparent 1px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.4) 1px, transparent 1px)
            `,
            backgroundSize: isMobile ? '30px 30px' : '50px 50px'
          }}
        />
      </div>

      {/* Sports figures */}
      <div className="absolute inset-0 pointer-events-none z-[5]">
        <div className="absolute top-20 left-20">
          <SportsFigure1 />
        </div>
        <div className="absolute top-40 right-32">
          <SportsFigure2 />
        </div>
        <div className="absolute bottom-32 left-40">
          <SportsFigure3 />
        </div>
        <div className="absolute bottom-20 right-20">
          <SportsFigure1 />
        </div>
        <div className="absolute top-60 left-1/2 transform -translate-x-1/2">
          <SportsFigure2 />
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 text-center px-6 max-w-6xl mx-auto flex flex-col justify-between min-h-[calc(100vh-5rem)]">
        {/* Main Content */}
        <div className="flex-1 flex flex-col justify-center">
        {/* Logo/Brand */}
        <motion.div
          className="mb-8 md:mb-10"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
                    <h1 className="text-4xl sm:text-5xl md:text-6xl font-black text-white mb-2">
            Grid<span className="text-emerald-400">२</span>Play
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-emerald-400 to-emerald-600 mx-auto rounded-full" />
        </motion.div>

                {/* Main heading */}
        <motion.div
          className="mb-8 md:mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-white leading-tight mb-2 md:mb-4 px-4 text-center">
            Book Now For Your
          </h2>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.8, ease: "easeOut" }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-center w-full flex justify-center px-4"
          >
            <TextRotator words={rotatingWords} className="text-emerald-400" />
          </motion.div>
        </motion.div>

                        {/* Subtitle */}
        <motion.p
          className="text-xl md:text-2xl lg:text-3xl font-semibold text-gray-200 mb-8 md:mb-10 max-w-3xl mx-auto px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          Discover, book, and play at the best venues. Fast. Easy. Anywhere.
        </motion.p>



        {/* Location Search Section */}
        <motion.div
          className="w-full max-w-md mx-auto mb-10 md:mb-12 location-search-container relative z-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.6 }}
        >
          <div className="relative">
            <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
            <input
              type="text"
              placeholder="Search location in Delhi NCR..."
              value={locationQuery}
              onChange={(e) => handleLocationSearch(e.target.value)}
              className="w-full pl-12 pr-12 py-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-full text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-300 text-base md:text-lg font-medium min-h-[48px]"
            />
            {isLocationSearching && (
              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 animate-spin" />
            )}
          </div>

          {/* Location Suggestions Dropdown */}
          <AnimatePresence>
            {showLocationSuggestions && locationSuggestions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-md rounded-xl shadow-xl border border-white/20 z-50 max-h-60 overflow-y-auto"
              >
                {locationSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleLocationSelect(suggestion)}
                    className="w-full px-4 py-3 text-left hover:bg-emerald-50 transition-colors duration-200 first:rounded-t-xl last:rounded-b-xl min-h-[44px] flex items-center"
                  >
                    <MapPin className="h-4 w-4 text-gray-500 mr-3 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-gray-900 font-medium truncate">
                        {suggestion.area || suggestion.city || 'Unknown Area'}
                      </div>
                      <div className="text-gray-600 text-sm truncate">
                        {suggestion.display_name || suggestion.city || 'Delhi, India'}
                      </div>
                    </div>
                  </button>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center items-center px-4 mb-8 md:mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              size="lg"
              onClick={() => navigate('/venues')}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 md:px-10 py-4 md:py-5 text-lg md:text-xl font-bold rounded-full shadow-lg hover:shadow-emerald-500/25 transition-all duration-300 w-full sm:w-auto min-h-[48px]"
            >
              Book now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>
          
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigate('/challenge')}
              className="border-emerald-400 text-emerald-400 hover:bg-emerald-400 hover:text-black px-8 md:px-10 py-4 md:py-5 text-lg md:text-xl font-bold rounded-full transition-all duration-300 w-full sm:w-auto min-h-[48px]"
            >
              Challenge Mode
            </Button>
          </motion.div>
        </motion.div>


        </div>



        {/* Scroll Indicator - Positioned after CTA buttons */}
        <motion.div
          className="flex flex-col items-center mt-6 mb-6 md:mt-8 md:mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.3 }}
        >
          <p className="text-emerald-300 text-base md:text-lg mb-3 font-bold">
            Scroll to explore more
          </p>
          <motion.div
            animate={{
              y: [0, 10, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <ChevronDown className="h-6 w-6 md:h-7 md:w-7 text-emerald-400" />
          </motion.div>
        </motion.div>

      </div>

      {/* Floating elements */}
      <motion.div
        className="absolute top-1/3 left-10 w-4 h-4 bg-emerald-400 rounded-full opacity-60"
        animate={{
          y: [0, -20, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div
        className="absolute bottom-1/3 right-10 w-6 h-6 bg-emerald-500 rounded-full opacity-40"
        animate={{
          y: [0, 20, 0],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
    </section>
  )
}

export default SportsBookingHero