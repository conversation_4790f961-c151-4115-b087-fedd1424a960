import React, { useState, useEffect, useRef, Suspense } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowRight, Calendar, Clock, User, ChevronRight, Activity, Zap, Target, Waves, Du<PERSON>bell, Trophy, Gamepad2, Bike, Mountain } from 'lucide-react';
import Header from '../components/Header';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { StreakBar } from '@/components/StreakBar';
import { NearbyVenues } from '@/components/NearbyVenues';
import HomepageAvailabilityWidget from '@/components/HomepageAvailabilityWidget';
import SportsBookingHero from "@/pages/SportsBookingHero";
import { getVenueDiscounts, VenueDiscount } from '@/utils/discountUtils';
import { VenueCard, VenueCardSkeleton } from '@/components/VenueCard';
import { useFeatureFlag } from '@/utils/featureFlags';
import { useSmartLocation } from '@/hooks/use-smart-location';
import { useIsMobile } from '@/hooks/use-mobile';

interface Venue {
  id: string;
  name: string;
  location: string;
  image_url: string;
  rating: number;
  review_count?: number;
  total_bookings?: number;
}
interface Sport {
  id: string;
  name: string;
  description: string;
  image_url: string;
}
const sportsQuotes = ["\"The more difficult the victory, the greater the happiness in winning.\" — Pelé", "\"You miss 100% of the shots you don't take.\" — Wayne Gretzky", "\"Champions keep playing until they get it right.\" — Billie Jean King", "\"It ain't over till it's over.\" — Yogi Berra", "\"The difference between the impossible and the possible lies in a person's determination.\" — Tommy Lasorda"];
const athletesBenefits = [{
  title: "Challenge Mode",
  description: "Create your team, challenge rivals, and climb the leaderboard in our competitive arena.",
  image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20-3.jpg"
}, {
  title: "Digital Training Log",
  description: "Keep a digital record of all your training sessions and track progress over time",
  image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20-2.jpg"
}, {
  title: "Team Communication",
  description: "Stay connected with your team and coaches through our integrated messaging platform",
  image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20-4.jpg"
}, {
  title: "Skill Development",
  description: "Access personalized training plans to develop your skills and reach your full potential",
  image: "https://images.unsplash.com/photo-1526506118085-60ce8714f8c5?q=80&w=1000&auto=format&fit=crop"
}];
const motivationalLines = [
  "Every champion was once a contender that refused to give up.",
  "The only bad workout is the one you didn't do.",
  "Push your limits, play your best.",
  "Winners train, losers complain.",
  "Greatness starts with a single step onto the court.",
  "Sweat now, shine later.",
  "Your only competition is yourself.",
  "Dream big. Train hard. Play harder.",
  "Hustle, hit, never quit.",
  "The game isn't over until you win."
];
const athleteFeatures = [
  {
    title: "Host & Join Tournaments",
    description: "Create and join competitive tournaments. Manage brackets, track results, and win prizes—Grid२Play's signature feature.",
    image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/e9c025786e85b8ef55cd86cc956403d7.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvZTljMDI1Nzg2ZTg1YjhlZjU1Y2Q4NmNjOTU2NDAzZDcuanBnIiwiaWF0IjoxNzUyMjc2MDE1LCJleHAiOjE3ODM4MTIwMTV9.R290K9D1ObUD3m4rvse54kxMuncSUFwRHmuHmzEPFx4",
    featured: true
  },
  {
    title: "Seamless Bookings",
    description: "Book your favorite sports slots in seconds with real-time availability and secure payments.",
    image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/bd9b839a78eccb003b8d22d5e7338d2a.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvYmQ5YjgzOWE3OGVjY2IwMDNiOGQyMmQ1ZTczMzhkMmEuanBnIiwiaWF0IjoxNzUyMjc1NjEyLCJleHAiOjE3ODM4MTE2MTJ9.3t6AYnUAbCM2QIOnyW5VYL5EaNli3B50rir6J4tsnxM"
  },
  {
    title: "AI Chat Assistant",
    description: "Get instant help, recommendations, and booking support with our smart AI assistant.",
    image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20-2.jpg"
  },
  {
    title: "Challenge Mode (Coming Soon!)",
    description: "Compete with friends and teams, climb the leaderboard, and win rewards in our upcoming Challenge Mode.",
    image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/dfc312f6e8e6e3d292b7b2bfd38cdce4.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvZGZjMzEyZjZlOGU2ZTNkMjkyYjdiMmJmZDM4Y2RjZTQuanBnIiwiaWF0IjoxNzUyMjc1NzM0LCJleHAiOjE3ODM4MTE3MzR9.STL2J4ipeuVieyQUVwz8hBH-KlBN3fO478bYpZU9hoU"
  },
  {
    title: "Personalized Experience",
    description: "Enjoy personalized greetings, motivational vibes, and recommendations tailored just for you.",
    image: "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/7f6cfe1bf61678f84139ff8095ee48d3.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvN2Y2Y2ZlMWJmNjE2NzhmODQxMzlmZjgwOTVlZTQ4ZDMuanBnIiwiaWF0IjoxNzUyMjc2MTAxLCJleHAiOjE3ODM4MTIxMDF9.kXhMNClA99w6X0-a3bO4lkVGvrR9vUUgsscZgWMvGDk"
  }
];
const Index: React.FC = () => {
  const navigate = useNavigate();
  const {
    user
  } = useAuth();

  // Mobile detection
  const isMobile = useIsMobile();

  // Feature flags
  const tournamentsEnabled = useFeatureFlag('TOURNAMENTS');

  // Smart location for nearby venues
  const smartLocation = useSmartLocation();

  // Filter features based on feature flags
  const filteredAthleteFeatures = athleteFeatures.filter(feature => {
    if (feature.title.toLowerCase().includes('tournament')) {
      return tournamentsEnabled;
    }
    return true;
  });

  // Removed isBookModalOpen state - now using navigation to BookingPage
  const [activeQuoteIndex, setActiveQuoteIndex] = useState(0);
  const [visibleSections, setVisibleSections] = useState({
    venues: false,
    sports: false,
    athletes: false,
    forYou: false,
    quotes: false
  });
  const [venues, setVenues] = useState<Venue[]>([]);
  const [sports, setSports] = useState<Sport[]>([]);
  const [loading, setLoading] = useState({
    venues: true,
    sports: true
  });

  const [locationPermissionHandled, setLocationPermissionHandled] = useState(false);
  const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
  const [greeting, setGreeting] = useState('');
  const [mostPlayedSport, setMostPlayedSport] = useState<string | null>(null);
  const [motivationalLine, setMotivationalLine] = useState('');
  const [venueDiscounts, setVenueDiscounts] = useState<Record<string, VenueDiscount>>({});

  const venuesRef = useRef<HTMLDivElement>(null);
  const sportsRef = useRef<HTMLDivElement>(null);
  const athletesRef = useRef<HTMLDivElement>(null);
  const forYouRef = useRef<HTMLDivElement>(null);
  const quotesRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    // Fetch real data from Supabase
    fetchVenues();
    fetchSports();
    const quoteInterval = setInterval(() => {
      setActiveQuoteIndex(prev => (prev + 1) % sportsQuotes.length);
    }, 5000);
    return () => clearInterval(quoteInterval);
  }, []);
  const fetchVenues = async () => {
    try {
      const {
        data,
        error
      } = await supabase.from('venues').select('id, name, location, image_url, rating').eq('is_active', true).order('rating', {
        ascending: false
      }).limit(4);
      if (error) throw error;
      if (data) {
        setVenues(data);

        // Fetch discounts for venues
        const venueIds = data.map(venue => venue.id);
        if (venueIds.length > 0) {
          const discounts = await getVenueDiscounts(venueIds);
          setVenueDiscounts(discounts);
        }
      }
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setLoading(prev => ({
        ...prev,
        venues: false
      }));
    }
  };
  const fetchSports = async () => {
    try {
      const {
        data,
        error
      } = await supabase.from('sports').select('id, name, description, image_url').eq('is_active', true).limit(4);
      if (error) throw error;
      if (data) {
        setSports(data);
      }
    } catch (error) {
      console.error('Error fetching sports:', error);
    } finally {
      setLoading(prev => ({
        ...prev,
        sports: false
      }));
    }
  };
  useEffect(() => {
    const observerOptions = {
      threshold: 0.2
    };
    const observerCallback = (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.id;
          setVisibleSections(prev => ({
            ...prev,
            [sectionId]: true
          }));
        }
      });
    };
    const observer = new IntersectionObserver(observerCallback, observerOptions);
    if (venuesRef.current) observer.observe(venuesRef.current);
    if (sportsRef.current) observer.observe(sportsRef.current);
    if (athletesRef.current) observer.observe(athletesRef.current);
    if (forYouRef.current) observer.observe(forYouRef.current);
    if (quotesRef.current) observer.observe(quotesRef.current);
    return () => observer.disconnect();
  }, []);
  const handleSportCardClick = (sportId: string) => {
    navigate(`/venues?sport=${sportId}`);
  };
  const handleLocationPermissionGranted = () => {
    setLocationPermissionHandled(true);
    setLocationPermissionGranted(true);
    console.log("Location permission granted");
  };

  const handleLocationPermissionDenied = () => {
    setLocationPermissionHandled(true);
    setLocationPermissionGranted(false);
    console.log("Location permission denied");
  };



  useEffect(() => {
    const now = new Date();
    const hour = now.getHours();
    let timeGreeting = 'Welcome back';
    if (hour < 12) timeGreeting = 'Good morning';
    else if (hour < 18) timeGreeting = 'Good afternoon';
    else timeGreeting = 'Good evening';

    if (user) {
      const name = user?.user_metadata?.full_name || '';
      setGreeting(`${timeGreeting}${name ? ', ' + name : ''}!`);
      setMotivationalLine(motivationalLines[Math.floor(Math.random() * motivationalLines.length)]);
    } else {
      setGreeting("Welcome, Athlete!");
      setMotivationalLine("Ready to play? Sign in and join the action!");
    }
  }, [user]);

  useEffect(() => {
    // Fetch user's most played sport
    const fetchMostPlayedSport = async () => {
      if (!user) return;
      const { data, error } = await supabase
        .from('bookings')
        .select('court:courts(sport:sports(name, id))')
        .eq('user_id', user.id)
        .in('status', ['confirmed', 'completed']);
      if (error || !data) return;
      const sportCount: Record<string, { name: string; count: number }> = {};
      data.forEach((b: any) => {
        const sport = b.court?.sport;
        if (sport && sport.id) {
          if (!sportCount[sport.id]) sportCount[sport.id] = { name: sport.name, count: 0 };
          sportCount[sport.id].count++;
        }
      });
      const sorted = Object.values(sportCount).sort((a, b) => b.count - a.count);
      if (sorted.length > 0) {
        setMostPlayedSport(sorted[0].name);
        setMotivationalLine(`Keep dominating the field, ${user.user_metadata?.full_name?.split(' ')[0] || user.email?.split('@')[0] || ''}! You're a true ${sorted[0].name} star.`);
      } else {
        setMostPlayedSport(null);
        setMotivationalLine('Ready to play? Book your favorite sport and get started!');
      }
    };
    fetchMostPlayedSport();
  }, [user]);

  // Sports doodle pattern SVG for reuse
  const sportsDoodlePattern = `url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cstyle%3E.doodle%7Bfill:none;stroke-width:1.5;%7D.grey%7Bstroke:%23666666;%7D.green%7Bstroke:%2310b981;%7D%3C/style%3E%3C/defs%3E%3C!-- Football/Soccer --%3E%3Ccircle cx='25' cy='30' r='8' class='doodle grey'/%3E%3Cpath d='M20 25l10 0M17 30l16 0M20 35l10 0' class='doodle grey'/%3E%3C!-- Basketball --%3E%3Ccircle cx='160' cy='45' r='9' class='doodle green'/%3E%3Cpath d='M151 45h18M160 36v18M155 40l10 10M155 50l10-10' class='doodle green'/%3E%3C!-- Tennis Racket --%3E%3Cellipse cx='70' cy='25' rx='6' ry='10' class='doodle grey'/%3E%3Cpath d='M70 35l0 8' class='doodle grey'/%3E%3Cpath d='M64 25h12M70 20v10' class='doodle grey'/%3E%3C!-- Cricket Bat --%3E%3Cpath d='M130 20l8 0l0 15l-8 0z' class='doodle green'/%3E%3Cpath d='M134 35l0 8' class='doodle green'/%3E%3C!-- Badminton Shuttlecock --%3E%3Cpath d='M45 70l0-8l6 4l6-4l0 8l-6 4z' class='doodle grey'/%3E%3Cpath d='M51 62l0-5' class='doodle grey'/%3E%3C!-- Swimming --%3E%3Cpath d='M15 85c3 0 6-2 9-2s6 2 9 2s6-2 9-2s6 2 9 2' class='doodle green'/%3E%3Cpath d='M15 90c3 0 6-2 9-2s6 2 9 2s6-2 9-2s6 2 9 2' class='doodle green'/%3E%3C!-- Volleyball --%3E%3Ccircle cx='170' cy='85' r='8' class='doodle grey'/%3E%3Cpath d='M162 85h16M170 77v16M165 80l10 10' class='doodle grey'/%3E%3C!-- Running Figure --%3E%3Ccircle cx='95' cy='75' r='3' class='doodle green'/%3E%3Cpath d='M95 78l0 12M95 85l-5 8M95 85l5 8M95 82l-4-2M95 82l6 2' class='doodle green'/%3E%3C!-- Table Tennis Paddle --%3E%3Cellipse cx='125' cy='80' rx='5' ry='7' class='doodle grey'/%3E%3Cpath d='M125 87l0 6' class='doodle grey'/%3E%3C!-- Hockey Stick --%3E%3Cpath d='M30 120l15 0l0 3l-12 0l0 8' class='doodle green'/%3E%3C!-- Golf Club --%3E%3Cpath d='M180 120l0 15M180 135l-3 0' class='doodle grey'/%3E%3Ccircle cx='177' cy='138' r='1.5' class='doodle grey'/%3E%3C!-- Boxing Glove --%3E%3Cpath d='M65 115c0-3 3-5 7-5s7 2 7 5l0 8c0 3-3 5-7 5s-7-2-7-5z' class='doodle green'/%3E%3Cpath d='M65 120l-3 0l0 5l3 0' class='doodle green'/%3E%3C!-- Dumbbell --%3E%3Cpath d='M110 115l0 10M107 115l6 0M107 125l6 0M105 113l0 4M115 113l0 4M105 125l0 4M115 125l0 4' class='doodle grey'/%3E%3C!-- Whistle --%3E%3Cellipse cx='150' cy='120' rx='4' ry='2' class='doodle green'/%3E%3Ccircle cx='148' cy='120' r='1' class='doodle green'/%3E%3Cpath d='M154 120l3 0' class='doodle green'/%3E%3C!-- Trophy --%3E%3Cpath d='M25 155l0 8l10 0l0-8M30 155l0-5M27 150l6 0M25 163l10 0M27 166l6 0' class='doodle grey'/%3E%3Cpath d='M22 152l3 0M35 152l3 0' class='doodle grey'/%3E%3C!-- Medal --%3E%3Ccircle cx='170' cy='160' r='5' class='doodle green'/%3E%3Cpath d='M167 150l6 0l-3 5z' class='doodle green'/%3E%3Cpath d='M170 157l0 1' class='doodle green'/%3E%3C!-- Stopwatch --%3E%3Ccircle cx='80' cy='160' r='6' class='doodle grey'/%3E%3Cpath d='M80 154l0 6l4 0' class='doodle grey'/%3E%3Cpath d='M78 148l4 0' class='doodle grey'/%3E%3C!-- Target/Dartboard --%3E%3Ccircle cx='120' cy='160' r='7' class='doodle green'/%3E%3Ccircle cx='120' cy='160' r='4' class='doodle green'/%3E%3Ccircle cx='120' cy='160' r='2' class='doodle green'/%3E%3C!-- Sneaker --%3E%3Cpath d='M45 180c0-2 2-3 5-3l8 0c3 0 5 1 5 3l0 3l-18 0z' class='doodle grey'/%3E%3Cpath d='M47 183l14 0l0 2l-14 0z' class='doodle grey'/%3E%3Cpath d='M50 177l0 3M53 177l0 3M56 177l0 3' class='doodle grey'/%3E%3C!-- Water Bottle --%3E%3Cpath d='M140 175l0 15l6 0l0-15M142 175l0-3l2 0l0 3' class='doodle green'/%3E%3Cpath d='M141 180l4 0M141 185l4 0' class='doodle green'/%3E%3C!-- Gym Weight Plate --%3E%3Ccircle cx='90' cy='185' r='6' class='doodle grey'/%3E%3Ccircle cx='90' cy='185' r='3' class='doodle grey'/%3E%3C!-- Sports Cap --%3E%3Cpath d='M160 180c0-3 4-5 8-5s8 2 8 5l-2 0l0 3l-12 0l0-3z' class='doodle green'/%3E%3Cpath d='M174 183l4 0' class='doodle green'/%3E%3C/svg%3E")`;

  // Function to get sport image from Supabase storage based on sport name
  const getSportImage = (sportName: string) => {
    const name = sportName.toLowerCase();
    if (name.includes('box football') || name === 'box football') {
      return 'https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/ded93de8b21676306d7fed77636b856c.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvZGVkOTNkZThiMjE2NzYzMDZkN2ZlZDc3NjM2Yjg1NmMuanBnIiwiaWF0IjoxNzUxODMwNDUxLCJleHAiOjE3ODMzNjY0NTF9.m6Wb395LYd9zST8i2t-lchc7OHOBJ8fwJEuOGPBuTvw';
    }
    if (name.includes('box cricket') || name === 'box cricket') {
      return 'https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/e79e2acd3c6b7ca61e7c6674de54dced.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvZTc5ZTJhY2QzYzZiN2NhNjFlN2M2Njc0ZGU1NGRjZWQuanBnIiwiaWF0IjoxNzUxODkwOTEyLCJleHAiOjE3ODM0MjY5MTJ9.cNcHW7wtMivvvGF_E0LMhwOMj5Q4BhgOudk2U3zMisw';
    }
    {
    }
    if (name.includes('badminton') || name === 'badminton') {
      return 'https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/dc5c864a912ced48688ddfff5368e597.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvZGM1Yzg2NGE5MTJjZWQ0ODY4OGRkZmZmNTM2OGU1OTcuanBnIiwiaWF0IjoxNzUxODMxOTU2LCJleHAiOjE3ODMzNjc5NTZ9.UL9tw0ctSZXib9YZDziGioMnGrLgP5Rv7UG06AedjbU';
    }
    if (name.includes('swimming') || name === 'swimming') {
      return 'https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/sign/sports/3c9818ba155419c3a273175f3ac52aa5.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kMGI0Nzg1NC0yN2JlLTRjMzktYTBiNi04YmRmMjg0OWUwNjEiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzcG9ydHMvM2M5ODE4YmExNTU0MTljM2EyNzMxNzVmM2FjNTJhYTUuanBnIiwiaWF0IjoxNzUxODMwNTAwLCJleHAiOjE3ODMzNjY1MDB9.GzWTlJ7jGCJG3HRYaSUJhRM6WPw_V4qwfxfN7S5kruo';
    }
    // Return null for sports without specific images - will use icon fallback
    return null;
  };

  // Function to get sport icon based on sport name (fallback for sports without images)
  const getSportIcon = (sportName: string) => {
    const name = sportName.toLowerCase();
    if (name.includes('football') || name.includes('soccer')) return Zap;
    if (name.includes('basketball')) return Target;
    if (name.includes('tennis')) return Activity;
    if (name.includes('swimming')) return Waves;
    if (name.includes('gym') || name.includes('fitness')) return Dumbbell;
    if (name.includes('badminton')) return Trophy;
    if (name.includes('cricket')) return Gamepad2;
    if (name.includes('cycling') || name.includes('bike')) return Bike;
    if (name.includes('climbing') || name.includes('rock')) return Mountain;
    // Default icon for other sports
    return Activity;
  };

  return (
    <div className="min-h-screen bg-navy-dark text-card-foreground relative">
      {/* Sports Doodle Background Pattern for Main Dark Background */}
      <div
        className="absolute inset-0 opacity-[0.28] pointer-events-none"
        style={{
          backgroundImage: sportsDoodlePattern,
          backgroundSize: '200px 200px',
          backgroundRepeat: 'repeat'
        }}
      />
      <Header />

      <section className="hero-section">
        <SportsBookingHero />
      </section>

      {/* Personalized Greeting Section - Now visible on mobile */}
      {!locationPermissionHandled && (
        <div className="container mx-auto px-4 pt-6 md:pt-8 pb-4 relative z-10">
          <div className="bg-gradient-to-r from-indigo-500/10 to-green-500/10 rounded-xl p-4 md:p-6 border border-indigo-500/20 flex flex-col items-center text-center">
            <h2 className="text-lg md:text-xl font-semibold text-white mb-2">{greeting}</h2>
            <p className="text-green-300 text-sm mb-4">{motivationalLine}</p>
            {!user && (
              <button
                className="px-6 py-3 bg-emerald-600 text-white rounded-lg font-semibold hover:bg-emerald-700 transition min-h-[44px] w-full sm:w-auto"
                onClick={() => navigate('/login')}
              >
                Sign In
              </button>
            )}
          </div>
        </div>
      )}

      {/* Tournament Hero CTA - Prominent placement for key USP */}
      {tournamentsEnabled && (
        <section className="py-12 bg-gradient-to-r from-emerald-900 via-emerald-800 to-emerald-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                🏆 Join Competitive Tournaments
              </h2>
              <p className="text-emerald-100 text-lg md:text-xl mb-8">
                Grid२Play's signature feature: Create, join, and compete in tournaments with prize pools and leaderboards
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => navigate('/tournaments')}
                  className="px-8 py-4 bg-white text-emerald-900 rounded-full font-bold text-lg hover:bg-emerald-50 transition-all duration-300 shadow-lg hover:shadow-xl min-h-[48px]"
                >
                  Browse Tournaments
                </button>
                <button
                  onClick={() => navigate('/tournaments/create')}
                  className="px-8 py-4 bg-emerald-600 text-white rounded-full font-bold text-lg hover:bg-emerald-700 transition-all duration-300 border-2 border-emerald-400 min-h-[48px]"
                >
                  Host Tournament
                </button>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Near You Section - Show for GPS location, user preference location, or manual permission grant */}
      {(locationPermissionGranted || ((smartLocation.source === 'gps' || smartLocation.source === 'preference') && smartLocation.coordinates)) && (
        <div className="pt-4 pb-8">
          <NearbyVenues />
        </div>
      )}

      <section id="venues" ref={venuesRef} className={`${isMobile ? 'py-8' : 'py-16'} bg-gradient-to-b from-black/90 to-navy-dark relative`}>
        {/* Sports Doodle Pattern for Dark Gradient Background */}
        <div
          className="absolute inset-0 opacity-[0.30] pointer-events-none"
          style={{
            backgroundImage: sportsDoodlePattern,
            backgroundSize: '200px 200px',
            backgroundRepeat: 'repeat'
          }}
        />
        <div className={`container mx-auto ${isMobile ? 'px-4 pb-8' : 'px-4 pb-24'} relative z-10`}>
          <div className={`flex justify-between items-center ${isMobile ? 'mb-4' : 'mb-8'} ${visibleSections.venues ? 'animate-reveal' : 'opacity-0'}`}>
            <h2 className={`section-title text-white relative ${isMobile ? 'text-xl' : ''}`}>
              Featured Venues
              <span className="absolute -bottom-2 left-0 w-20 h-1 bg-indigo-light"></span>
            </h2>
            <Link to="/venues" className={`text-indigo-light font-semibold flex items-center group hover:text-indigo-dark transition-colors ${isMobile ? 'text-sm' : ''}`}>
              View All <ChevronRight className={`ml-1 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'} group-hover:translate-x-1 transition-transform`} />
            </Link>
          </div>

          <div className={`${visibleSections.venues ? 'animate-reveal' : 'opacity-0'}`}>
            {loading.venues ? (
              // Always horizontal carousel for loading state
              <Carousel className="w-full">
                <CarouselContent className="-ml-4">
                  {[1, 2, 3, 4].map((_, index) => (
                    <CarouselItem key={index} className="pl-4 basis-auto">
                      <VenueCardSkeleton variant={isMobile ? "compact" : "default"} />
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
            ) : venues.length > 0 ? (
              // Always horizontal carousel for venues
              <Carousel className="w-full">
                <CarouselContent className="-ml-4">
                  {venues.map((venue, index) => (
                    <CarouselItem key={venue.id} className="pl-4 basis-auto">
                      <div
                        className="animate-fade-in"
                        style={{animationDelay: `${0.1 * (index + 1)}s`}}
                      >
                        <VenueCard
                          venue={venue}
                          discount={venueDiscounts[venue.id]}
                          variant={isMobile ? "compact" : "default"}
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                {!isMobile && (
                  <>
                    <CarouselPrevious className="left-2" />
                    <CarouselNext className="right-2" />
                  </>
                )}
              </Carousel>
            ) : (
              <div className={`text-center ${isMobile ? 'py-6' : 'py-8 md:py-12'} bg-black rounded-lg`}>
                <p className="text-white text-lg mb-4">No venues found</p>
                <button onClick={() => navigate('/venues')} className="px-4 py-2 bg-[#1E3B2C] text-white rounded-md hover:bg-[#2E7D32] transition-colors min-h-[44px]">
                  Browse All Venues
                </button>
              </div>
            )}
          </div>
        </div>
      </section>

     <section id="sports" ref={sportsRef} className={`${isMobile ? 'py-8' : 'py-16'} bg-gradient-to-b from-navy-dark to-black/90 relative`}>
  {/* Sports Doodle Pattern for Dark Gradient Background */}
  <div
    className="absolute inset-0 opacity-[0.30] pointer-events-none"
    style={{
      backgroundImage: sportsDoodlePattern,
      backgroundSize: '200px 200px',
      backgroundRepeat: 'repeat'
    }}
  />
  <div className="container mx-auto px-4 relative z-10">
    <div className={`flex justify-between items-center ${isMobile ? 'mb-4' : 'mb-10'} ${visibleSections.sports ? 'animate-reveal' : 'opacity-0'}`}>
      <h2 className={`section-title text-white relative ${isMobile ? 'text-xl' : ''}`}>
        Featured Sports
        <span className="absolute -bottom-2 left-0 w-20 h-1 bg-indigo-light"></span>
      </h2>
      <Link to="/sports" className={`text-indigo-light font-semibold flex items-center group hover:text-indigo-dark transition-colors ${isMobile ? 'text-sm' : ''}`}>
        View All <ChevronRight className={`ml-1 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'} group-hover:translate-x-1 transition-transform`} />
      </Link>
    </div>

    <div className={`${visibleSections.sports ? 'animate-reveal' : 'opacity-0'}`}>
      {loading.sports ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo"></div>
        </div>
      ) : (
        <div className={`grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-2 md:grid-cols-4 gap-4 md:gap-6'}`}>
          {/* Dynamic Sports from Database */}
          {sports.map((sport) => {
            const sportImage = getSportImage(sport.name);
            const SportIcon = getSportIcon(sport.name);

            return (
              <div
                key={sport.id}
                className={`group cursor-pointer flex flex-col items-center ${
                  isMobile
                    ? 'p-3 rounded-2xl bg-black/50 border border-emerald-500/20 hover:border-emerald-400/50 transition-all duration-300 hover:bg-emerald-900/10 min-h-[100px]'
                    : 'p-4 md:p-6 rounded-xl bg-black/50 border border-emerald-500/20 hover:border-emerald-400/50 transition-all duration-300 hover:bg-emerald-900/10 min-h-[120px] md:min-h-[140px]'
                }`}
                onClick={() => handleSportCardClick(sport.id)}
                style={{ minHeight: '48px' }} // Ensure touch target compliance
              >
                <div className={`${
                  isMobile
                    ? 'w-12 h-12 rounded-xl mb-2'
                    : 'w-20 h-20 md:w-24 md:h-24 rounded-xl mb-3'
                } group-hover:scale-105 transition-transform duration-300 overflow-hidden`}>
                  {sportImage ? (
                    // Use Supabase image if available
                    <img
                      src={sportImage}
                      alt={sport.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    // Use icon fallback for sports without specific images
                    <div className="w-full h-full bg-gradient-to-br from-emerald-600 to-emerald-800 flex items-center justify-center">
                      <SportIcon className={`${isMobile ? 'w-6 h-6' : 'w-10 h-10 md:w-12 md:h-12'} text-white`} />
                    </div>
                  )}
                </div>
                <span className={`text-white ${
                  isMobile
                    ? 'text-xs font-medium text-center'
                    : 'text-sm md:text-base font-medium text-center'
                } group-hover:text-emerald-400 transition-colors`}>
                  {sport.name}
                </span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  </div>
</section>

      <section id="athletes" ref={athletesRef} className="py-16 bg-gradient-to-b from-black/90 to-navy-dark relative">
        {/* Sports Doodle Pattern for Dark Gradient Background */}
        <div
          className="absolute inset-0 opacity-[0.30] pointer-events-none"
          style={{
            backgroundImage: sportsDoodlePattern,
            backgroundSize: '200px 200px',
            backgroundRepeat: 'repeat'
          }}
        />
        <div className="container mx-auto px-4 relative z-10">
          <div className={`mb-10 ${visibleSections.athletes ? 'animate-reveal' : 'opacity-0'}`}>
            <h2 className="section-title text-white text-center relative">
              For Athletes
              <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-indigo-light"></span>
            </h2>
            <p className="text-gray-300 text-center max-w-3xl mx-auto mt-4">
              Discover what makes our platform unique and perfect for your sports journey.
            </p>
          </div>
          {/* Desktop Grid */}
          <div className={`hidden md:grid md:grid-cols-2 lg:grid-cols-5 gap-6 ${visibleSections.athletes ? 'animate-reveal' : 'opacity-0'}`}>
            {filteredAthleteFeatures.map((feature, index) => (
              <div key={index} className="group" style={{ animationDelay: `${0.15 * (index + 1)}s` }}>
                <div className="overflow-hidden rounded-lg bg-navy relative h-80">
                  <img src={feature.image} alt={feature.title} className="w-full h-full object-cover filter grayscale group-hover:grayscale-0 transition-all duration-500" />
                  <div className="absolute inset-0 bg-gradient-to-t from-navy to-transparent opacity-70" />
                  <div className="absolute inset-0 p-6 flex flex-col justify-end">
                    <h3 className="text-xl font-bold group-hover:text-white transition-colors">{feature.title}</h3>
                    <p className="text-gray-300 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-500">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {/* Mobile Carousel */}
          <div className={`md:hidden ${visibleSections.athletes ? 'animate-reveal' : 'opacity-0'}`}>
            <Carousel className="w-full">
              <CarouselContent className="gap-4">
                {filteredAthleteFeatures.map((feature, index) => (
                  <CarouselItem
                    key={index}
                    className="basis-[80%] px-2"
                    style={{ scrollSnapAlign: 'center' }}
                  >
                    <div className="relative h-[300px] rounded-xl overflow-hidden bg-navy group active:scale-95 transition-all duration-300">
                      <img src={feature.image} alt={feature.title} className="w-full h-full object-cover brightness-90 group-hover:brightness-100 group-hover:scale-105 transition-all duration-500" loading="lazy" />
                      <div className="absolute inset-0 bg-gradient-to-t from-navy via-navy/50 to-transparent opacity-90" />
                      <div className="absolute inset-x-0 bottom-0 p-4">
                        <div className="bg-navy/80 backdrop-blur-sm rounded-lg p-4 border-t border-indigo/20 transform translate-y-0 group-hover:-translate-y-2 transition-transform duration-300">
                          <h3 className="text-lg font-bold text-white mb-2 group-hover:text-[#2def80] transition-colors">{feature.title}</h3>
                          <p className="text-sm text-gray-300 line-clamp-2 group-hover:line-clamp-none transition-all duration-300">{feature.description}</p>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
                <CarouselPrevious className="relative inset-0 translate-y-0 h-8 w-8 rounded-full bg-navy hover:bg-indigo text-white border border-indigo/20 hover:border-indigo transition-colors" />
                <CarouselNext className="relative inset-0 translate-y-0 h-8 w-8 rounded-full bg-navy hover:bg-indigo text-white border border-indigo/20 hover:border-indigo transition-colors" />
              </div>
            </Carousel>
          </div>
          <div className="mt-10 text-center">
            <Link to="/register" className="inline-flex items-center px-6 py-3 bg-indigo text-white rounded-md hover:bg-indigo-dark transition-colors">
              Join Now
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      <section id="forYou" ref={forYouRef} className={`${isMobile ? 'py-8' : 'py-16'} bg-gradient-to-r from-navy-light to-navy-dark relative`}>
        {/* Sports Doodle Pattern for Lighter Background - Lower Opacity */}
        <div
          className="absolute inset-0 opacity-[0.12] pointer-events-none"
          style={{
            backgroundImage: sportsDoodlePattern,
            backgroundSize: '200px 200px',
            backgroundRepeat: 'repeat'
          }}
        />
        <div className="container mx-auto px-4 relative z-10">
          <h2 className={`section-title text-center text-white ${isMobile ? 'mb-4 text-xl' : 'mb-8'} ${visibleSections.forYou ? 'animate-reveal' : 'opacity-0'}`}>
            For You
            <span className="block w-20 h-1 bg-indigo-light mx-auto mt-2"></span>
          </h2>

          <div className={`max-w-4xl mx-auto ${visibleSections.forYou ? 'animate-reveal' : 'opacity-0'}`} style={{
            animationDelay: '0.2s'
          }}>
            <div className={`glass-card shadow-2xl overflow-hidden bg-navy/50 ${isMobile ? 'rounded-2xl' : ''}`}>
              <div className={`${isMobile ? 'p-4' : 'p-6'}`}>
                <div className={`${isMobile ? 'mb-4' : 'mb-6'} flex items-center`}>
                  <Activity className={`${isMobile ? 'w-5 h-5' : 'w-6 h-6'} text-indigo-light mr-3`} />
                  <h3 className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold text-white`}>Recommended For You</h3>
                </div>

                <StreakBar />

                <div className={`${isMobile ? 'my-4' : 'my-6'}`}>
                  <Suspense fallback={<div className={`${isMobile ? 'h-[150px]' : 'h-[200px]'} bg-navy animate-pulse rounded-lg`}></div>}>
                    <HomepageAvailabilityWidget />
                  </Suspense>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex flex-col items-center text-center p-4 rounded-lg border border-indigo/30 hover:border-indigo-light bg-navy/50 hover:bg-navy transition-all">
                    <Calendar className="w-8 h-8 mb-3 text-indigo-light" />
                    <h4 className="text-base font-semibold mb-1 text-white">Quick Booking</h4>
                    <p className="text-sm text-gray-400">Based on your preferences</p>
                    <button onClick={() => navigate('/book')} className="mt-3 py-1.5 px-4 bg-indigo hover:bg-indigo-dark text-white text-sm rounded-full transition-colors">
                      Book Now
                    </button>
                  </div>

                  <div className="flex flex-col items-center text-center p-4 rounded-lg border border-indigo/30 hover:border-indigo-light bg-navy/50 hover:bg-navy transition-all">
                    <Clock className="w-8 h-8 mb-3 text-indigo-light" />
                    <h4 className="text-base font-semibold mb-1 text-white">Upcoming Event</h4>
                    <p className="text-sm text-gray-400">Community sports event</p>
                    <button className="mt-3 py-1.5 px-4 bg-indigo hover:bg-indigo-dark text-white text-sm rounded-full transition-colors">
                      Learn More
                    </button>
                  </div>

                  <div className="flex flex-col items-center text-center p-4 rounded-lg border border-indigo/30 hover:border-indigo-light bg-navy/50 hover:bg-navy transition-all">
                    <User className="w-8 h-8 mb-3 text-indigo-light" />
                    <h4 className="text-base font-semibold mb-1 text-white">Complete Profile</h4>
                    <p className="text-sm text-gray-400">Get recommendations</p>
                    <Link to="/register" className="mt-3 py-1.5 px-4 bg-indigo hover:bg-indigo-dark text-white text-sm rounded-full transition-colors">
                      Sign Up
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="partners" ref={quotesRef} className="py-16 bg-indigo relative overflow-hidden">
  <div className="absolute inset-0 pattern-dots pattern-opacity-10 bg-navy-dark"></div>
  <div className="container mx-auto px-4 relative z-10">
    <h2 className={`text-3xl font-bold text-center text-white mb-10 ${visibleSections.quotes ? 'animate-reveal' : 'opacity-0'}`}>
      Become A Partner
      <span className="block w-20 h-1 bg-white mt-2 mx-auto"></span>
    </h2>

    <div className="relative w-full max-w-6xl mx-auto overflow-hidden">
      {/* Gradient fade edges */}
      <div className="absolute inset-y-0 left-0 w-24 bg-gradient-to-r from-indigo to-transparent z-10"></div>
      <div className="absolute inset-y-0 right-0 w-24 bg-gradient-to-l from-indigo to-transparent z-10"></div>

      {/* Infinite scrolling carousel */}
      <div className="flex overflow-x-hidden group hover:[animation-play-state:paused]">
        <div className="flex animate-infinite-scroll whitespace-nowrap">
          {[
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20-3.jpg",
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20.jpg",
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//ChatGPT%20Image%20Apr%2018,%202025,%2001_59_47%20AM.png",
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//3e6653285e2d08a596a15f28e50d2735.jpg"
          ].map((img, idx) => (
            <div key={idx} className="inline-flex items-center justify-center mx-4">
              <img
                src={img}
                alt={`Partner ${idx + 1}`}
                className="h-32 w-auto object-contain max-w-[200px] md:max-w-[300px] rounded-lg shadow-md hover:scale-105 transition-transform"
                loading="lazy"
              />
            </div>
          ))}
          {/* Duplicate for seamless looping */}
          {[
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20-3.jpg",
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//%20.jpg",
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//ChatGPT%20Image%20Apr%2018,%202025,%2001_59_47%20AM.png",
            "https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/venues//3e6653285e2d08a596a15f28e50d2735.jpg"
          ].map((img, idx) => (
            <div key={`dup-${idx}`} className="inline-flex items-center justify-center mx-4">
              <img
                src={img}
                alt={`Partner ${idx + 1}`}
                className="h-32 w-auto object-contain max-w-[200px] md:max-w-[300px] rounded-lg shadow-md hover:scale-105 transition-transform"
                loading="lazy"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
</section>

      <footer className="bg-gray-900 text-gray-300 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div>
              <h3 className="text-base font-semibold mb-3">Grid२Play</h3>
              <p className="text-sm text-gray-400">
                Book sports venues easily and efficiently with Grid२Play.
              </p>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-3">Quick Links</h3>
              <ul className="space-y-1">
                <li><Link to="/" className="text-sm hover:text-white">Home</Link></li>
                <li><Link to="/venues" className="text-sm hover:text-white">Venues</Link></li>
                <li><Link to="/sports" className="text-sm hover:text-white">Sports</Link></li>
                <li><Link to="/profile" className="text-sm hover:text-white">My Profile</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-3">Help & Support</h3>
              <ul className="space-y-1">
                <li><Link to="/faq" className="text-sm hover:text-white">FAQ</Link></li>
                <li><Link to="/help" className="text-sm hover:text-white">Help Center</Link></li>
                <li><Link to="/contact" className="text-sm hover:text-white">Contact Us</Link></li>
                <li><a href="tel:+919211848599" className="text-sm hover:text-white">Phone: +91 92118 48599</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-3">Legal & Privacy</h3>
              <ul className="space-y-1">
                <li><Link to="/privacy" className="text-sm hover:text-white">Privacy Policy</Link></li>
                <li><Link to="/terms" className="text-sm hover:text-white">Terms of Service</Link></li>
                <li><Link to="/cookie-policy" className="text-sm hover:text-white">Cookie Policy</Link></li>
                <li><button onClick={() => window.dispatchEvent(new CustomEvent('openCookieSettings'))} className="text-sm hover:text-white text-left">Cookie Settings</button></li>
              </ul>
            </div>
            <div>
              <h3 className="text-base font-semibold mb-3">Connect With Us</h3>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white">
                  <span className="sr-only">Facebook</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <span className="sr-only">Instagram</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-700 text-center text-gray-400">
            <p className="text-sm">&copy; 2020 Grid२Play. All rights reserved.</p>
            <p className="text-sm text-gray-400 text-center mt-1">
            Proudly Operating From India 🇮🇳
          </p>
          </div>
        </div>
      </footer>
      {/* BookSlotModal removed - now using BookingPage navigation */}
    </div>
  );
};

export default Index;