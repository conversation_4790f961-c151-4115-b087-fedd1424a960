
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, User, Lock, MessageCircle } from 'lucide-react';
import Header from '../components/Header';
import { toast } from '@/components/ui/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';

import { validatePhone, validatePassword, sanitizeInput } from '@/utils/security';
import { smsAuthService } from '@/services/smsAuthService';

const Register: React.FC = () => {
  // Mobile detection
  const isMobile = useIsMobile();

  // Common form fields
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // SMS registration fields
  const [phone, setPhone] = useState('');
  const countryCode = '+91'; // Hardcoded to India only
  const [otp, setOtp] = useState('');
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);

  // Form validation and loading states
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(300); // 5 minutes
    const timer = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Format timer display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Validate SMS registration form
  const validateSMSForm = () => {
    const errors: Record<string, string> = {};

    if (!name || name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    } else if (name.trim().length > 100) {
      errors.name = 'Name must be less than 100 characters';
    }

    if (!phone || !validatePhone(countryCode + phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0];
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle SMS registration (send OTP)
  const handleSMSRegistration = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateSMSForm()) {
      return;
    }

    setIsLoading(true);

    const sanitizedData = {
      name: sanitizeInput(name),
      phone: sanitizeInput(phone)
    };

    const fullPhone = countryCode + sanitizedData.phone;

    // Send SMS OTP
    const result = await smsAuthService.sendSMSOTP({
      phone: fullPhone,
      full_name: sanitizedData.name,
      password: password
    });

    setIsLoading(false);

    if (result.success) {
      setShowOtpInput(true);
      startOtpTimer();
      toast({
        title: "OTP Sent!",
        description: `We've sent a 6-digit OTP to your phone number ${fullPhone}`,
      });
    } else {
      // Check if it's a phone number already registered error
      const isPhoneAlreadyRegistered = result.error?.includes('already registered') ||
                                      result.error?.includes('phone number is already') ||
                                      result.error?.includes('Phone number already registered');

      if (isPhoneAlreadyRegistered) {
        toast({
          title: "Phone Number Already Registered",
          description: (
            <div className="space-y-2">
              <p>This phone number is already registered with Grid२Play.</p>
              <p className="text-sm">
                <span
                  className="text-emerald-400 underline cursor-pointer hover:text-emerald-300"
                  onClick={() => navigate('/login')}
                >
                  Sign in instead
                </span>
                {" "}or use a different phone number.
              </p>
            </div>
          ),
          variant: "destructive",
        });
      } else {
        toast({
          title: "Failed to send OTP",
          description: result.error || "Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  // Handle OTP verification
  const handleOtpVerification = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp || otp.length !== 6) {
      setFieldErrors({ otp: 'Please enter a valid 6-digit OTP' });
      return;
    }

    setIsLoading(true);

    const fullPhone = countryCode + sanitizeInput(phone);

    const result = await smsAuthService.verifySMSOTP({
      phone: fullPhone,
      otp: otp
    });

    setIsLoading(false);

    if (result.success) {
      // Prevent zoom after successful registration
      if (document.activeElement && document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }

      // Reset viewport to prevent zoom
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
      }

      toast({
        title: "Registration successful!",
        description: "Your phone number has been verified. Welcome to Grid2Play!",
      });

      // Navigate to email verification prompt
      navigate('/verify-email-prompt', {
        state: {
          user: result.user,
          registrationMethod: 'sms'
        }
      });
    } else {
      toast({
        title: "OTP verification failed",
        description: result.error || "Please check your OTP and try again.",
        variant: "destructive",
      });
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-[#0F2419] to-[#1E3B2C] relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#2E7D32]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-600/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <Header />

      <div className={`relative z-10 min-h-screen flex ${isMobile ? 'pt-16' : 'pt-20'}`}>
        {/* Left Side - Sports Imagery (Hidden on mobile) */}
        <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
          {/* Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#2E7D32] via-emerald-600 to-green-700"></div>

          {/* Sports Pattern Overlay */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-16 h-16 border-2 border-white rounded-full"></div>
            <div className="absolute top-40 right-32 w-12 h-12 border-2 border-white rounded-lg rotate-45"></div>
            <div className="absolute bottom-40 left-32 w-20 h-20 border-2 border-white rounded-full"></div>
            <div className="absolute bottom-20 right-20 w-14 h-14 border-2 border-white rounded-lg rotate-12"></div>
          </div>

          {/* Content Overlay */}
          <div className="relative z-10 flex flex-col justify-center px-12 text-white">
            <div className="max-w-md">
              <h2 className="text-4xl font-bold mb-6 leading-tight">
                Start Your <span className="text-emerald-200">Sports Journey</span> Today
              </h2>
              <p className="text-xl mb-8 text-emerald-100 leading-relaxed">
                Join thousands of sports enthusiasts and discover amazing venues near you.
              </p>

              {/* Feature Points */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-emerald-300 rounded-full"></div>
                  <span className="text-emerald-100">Quick SMS Verification</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-emerald-300 rounded-full"></div>
                  <span className="text-emerald-100">Instant Access to Venues</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-emerald-300 rounded-full"></div>
                  <span className="text-emerald-100">Secure & Trusted Platform</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Registration Form */}
        <div className={`w-full lg:w-1/2 flex items-center justify-center ${isMobile ? 'p-4 pt-8' : 'p-4 pt-20 lg:pt-4'} lg:bg-black`}>
          <div className="w-full max-w-md">
            {/* Main Card */}
            <div className={`backdrop-blur-2xl bg-gradient-to-br from-black/60 via-[#0F2419]/80 to-black/60 border border-[#2E7D32]/30 ${isMobile ? 'rounded-2xl' : 'rounded-3xl'} shadow-2xl overflow-hidden relative lg:bg-black lg:from-black lg:via-gray-900 lg:to-black lg:border-emerald-500/30`}>
              {/* Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-[#2E7D32]/20 via-transparent to-emerald-500/20 opacity-50 blur-xl"></div>

            {/* Content */}
            <div className="relative z-10">
              {/* Header */}
              <div className={`${isMobile ? 'px-4 py-6' : 'px-6 sm:px-8 py-8 sm:py-10'} text-center`}>
                {/* Logo with Animation */}
                <div className={`relative ${isMobile ? 'mb-4' : 'mb-6'}`}>
                  <div className={`${isMobile ? 'w-24 h-16' : 'w-28 h-20 sm:w-32 sm:h-24'} bg-gradient-to-br from-[#2E7D32] via-emerald-500 to-green-600 ${isMobile ? 'rounded-2xl' : 'rounded-3xl'} flex items-center justify-center mx-auto shadow-2xl transform hover:scale-105 transition-all duration-300 relative overflow-hidden`}>
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <span className={`${isMobile ? 'text-base' : 'text-lg sm:text-xl'} font-bold text-white relative z-10`}>Grid२Play</span>
                    {/* Shine Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full animate-shine"></div>
                  </div>
                  {/* Floating Particles */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                    <div className="w-2 h-2 bg-[#2E7D32] rounded-full animate-bounce delay-100"></div>
                  </div>
                  <div className="absolute top-4 right-1/4">
                    <div className="w-1 h-1 bg-emerald-400 rounded-full animate-ping delay-300"></div>
                  </div>
                  <div className="absolute top-4 left-1/4">
                    <div className="w-1 h-1 bg-green-500 rounded-full animate-ping delay-700"></div>
                  </div>
                </div>

                {/* Welcome Text */}
                <div className="space-y-2">
                  <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl sm:text-4xl'} font-bold bg-gradient-to-r from-white via-gray-100 to-emerald-100 bg-clip-text text-transparent`}>
                    Create Account
                  </h1>
                  <p className={`text-gray-400 ${isMobile ? 'text-sm' : 'text-sm sm:text-base'} font-medium`}>
                    Join Grid२Play today
                  </p>
                  <div className="flex items-center justify-center space-x-2 mt-4">
                    <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-[#2E7D32]"></div>
                    <div className="w-2 h-2 bg-[#2E7D32] rounded-full animate-pulse"></div>
                    <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-[#2E7D32]"></div>
                  </div>
                </div>
              </div>

              {/* Phone-First Registration Header */}
              <div className={`${isMobile ? 'mb-4' : 'mb-8'} text-center`}>
                <div className={`inline-flex items-center ${isMobile ? 'px-4 py-2' : 'px-6 py-3'} bg-gradient-to-r from-[#2E7D32]/20 to-emerald-500/20 ${isMobile ? 'rounded-xl' : 'rounded-2xl'} border border-[#2E7D32]/40 backdrop-blur-sm`}>
                  <MessageCircle className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} text-emerald-400 mr-3`} />
                  <span className={`text-emerald-300 ${isMobile ? 'text-xs' : 'text-sm'} font-semibold`}>SMS Registration</span>
                </div>
                <p className={`text-gray-400 ${isMobile ? 'text-xs' : 'text-sm'} ${isMobile ? 'mt-2' : 'mt-3'} font-medium`}>Secure account creation with phone verification</p>
              </div>

              {/* SMS Registration Form */}
              <div className={`${isMobile ? 'px-4 pb-6' : 'px-6 sm:px-8 pb-8'}`}>
                <form onSubmit={showOtpInput ? handleOtpVerification : handleSMSRegistration} className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
                  {!showOtpInput ? (
                    <>
                      {/* Full Name Field */}
                      <div className="space-y-2">
                        <label htmlFor="sms-name" className="block text-sm font-semibold text-emerald-300 mb-3">
                          Full Name
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <User className="h-5 w-5 text-emerald-400" />
                          </div>
                          <input
                            id="sms-name"
                            type="text"
                            value={name}
                            onChange={(e) => {
                              setName(e.target.value);
                              setFieldErrors(prev => ({ ...prev, name: '' }));
                            }}
                            className={`pl-12 w-full ${isMobile ? 'p-3' : 'p-4'} border ${fieldErrors.name ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all ${isMobile ? 'text-sm' : 'text-base'} backdrop-blur-sm hover:bg-black/80 placeholder-gray-500 min-h-[48px]`}
                            placeholder="Enter your full name"
                            required
                            maxLength={100}
                          />
                        </div>
                        {fieldErrors.name && <p className="text-red-400 text-sm mt-2 font-medium">{fieldErrors.name}</p>}
                      </div>

                      {/* Phone Number Field with Country Code */}
                      <div className="space-y-2">
                        <label htmlFor="sms-phone" className="block text-sm font-semibold text-emerald-300 mb-3">
                          Phone Number
                        </label>
                        <div className="flex gap-3">
                          <div className={`${isMobile ? 'w-20 p-3' : 'w-24 p-4'} border border-[#2E7D32]/40 bg-black/60 text-white rounded-xl flex items-center justify-center ${isMobile ? 'text-xs' : 'text-sm'} backdrop-blur-sm`}>
                            🇮🇳 +91
                          </div>
                          <div className="relative flex-1">
                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                              <MessageCircle className="h-5 w-5 text-emerald-400" />
                            </div>
                            <input
                              id="sms-phone"
                              type="tel"
                              value={phone}
                              onChange={(e) => {
                                setPhone(e.target.value);
                                setFieldErrors(prev => ({ ...prev, phone: '' }));
                              }}
                              className={`pl-12 w-full ${isMobile ? 'p-3' : 'p-4'} border ${fieldErrors.phone ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all ${isMobile ? 'text-sm' : 'text-base'} backdrop-blur-sm hover:bg-black/80 placeholder-gray-500 min-h-[48px]`}
                              placeholder="Enter your phone number"
                              required
                              maxLength={15}
                            />
                          </div>
                        </div>
                        {fieldErrors.phone && <p className="text-red-400 text-sm mt-2 font-medium">{fieldErrors.phone}</p>}
                        <p className="text-emerald-400 text-sm mt-2 font-medium">📱 We'll send a 6-digit OTP to this phone number</p>
                      </div>
                    </>
                  ) : (
                    /* OTP Input Field */
                    <div className="space-y-2">
                      <label htmlFor="otp" className="block text-sm font-semibold text-emerald-300 mb-3">
                        Enter OTP
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                          <MessageCircle className="h-5 w-5 text-emerald-400" />
                        </div>
                        <input
                          id="otp"
                          type="text"
                          value={otp}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                            setOtp(value);
                            setFieldErrors(prev => ({ ...prev, otp: '' }));
                          }}
                          className={`pl-12 w-full p-4 border ${fieldErrors.otp ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all text-center text-2xl tracking-widest backdrop-blur-sm hover:bg-black/80 placeholder-gray-500`}
                          placeholder="000000"
                          required
                          maxLength={6}
                        />
                      </div>
                      {fieldErrors.otp && <p className="text-red-400 text-sm mt-2 font-medium">{fieldErrors.otp}</p>}
                      <div className="flex justify-between items-center mt-3">
                        <p className="text-emerald-400 text-sm font-medium">
                          📱 OTP sent to {countryCode}{phone}
                        </p>
                        {otpTimer > 0 ? (
                          <p className="text-gray-400 text-sm">
                            Resend in {formatTimer(otpTimer)}
                          </p>
                        ) : (
                          <button
                            type="button"
                            onClick={() => {
                              setShowOtpInput(false);
                              setOtp('');
                            }}
                            className="text-emerald-400 text-sm hover:text-emerald-300 transition-colors font-medium hover:underline"
                          >
                            Change Number
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {!showOtpInput && (
                    <>
                      {/* Password Field */}
                      <div className="space-y-2">
                        <label htmlFor="sms-password" className="block text-sm font-semibold text-emerald-300 mb-3">
                          Password
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Lock className="h-5 w-5 text-emerald-400" />
                          </div>
                          <input
                            id="sms-password"
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => {
                              setPassword(e.target.value);
                              setFieldErrors(prev => ({ ...prev, password: '' }));
                            }}
                            className={`pl-12 pr-12 w-full p-4 border ${fieldErrors.password ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all text-base backdrop-blur-sm hover:bg-black/80 placeholder-gray-500`}
                            placeholder="Create a password"
                            required
                            maxLength={128}
                          />
                          <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="text-emerald-400 hover:text-white focus:outline-none transition-colors"
                            >
                              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                            </button>
                          </div>
                        </div>
                        {fieldErrors.password && <p className="text-red-400 text-sm mt-2 font-medium">{fieldErrors.password}</p>}
                      </div>

                      {/* Confirm Password Field */}
                      <div className="space-y-2">
                        <label htmlFor="sms-confirmPassword" className="block text-sm font-semibold text-emerald-300 mb-3">
                          Confirm Password
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Lock className="h-5 w-5 text-emerald-400" />
                          </div>
                          <input
                            id="sms-confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            value={confirmPassword}
                            onChange={(e) => {
                              setConfirmPassword(e.target.value);
                              setFieldErrors(prev => ({ ...prev, confirmPassword: '' }));
                            }}
                            className={`pl-12 pr-12 w-full p-4 border ${fieldErrors.confirmPassword ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all text-base backdrop-blur-sm hover:bg-black/80 placeholder-gray-500`}
                            placeholder="Confirm your password"
                            required
                            maxLength={128}
                          />
                          <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                            <button
                              type="button"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              className="text-emerald-400 hover:text-white focus:outline-none transition-colors"
                            >
                              {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                            </button>
                          </div>
                        </div>
                        {fieldErrors.confirmPassword && <p className="text-red-400 text-sm mt-2 font-medium">{fieldErrors.confirmPassword}</p>}
                      </div>
                    </>
                  )}

                  {/* Submit Button */}
                  <div className={`${isMobile ? 'pt-3' : 'pt-4'}`}>
                    <button
                      type="submit"
                      className={`w-full ${isMobile ? 'py-3 px-4' : 'py-4 px-6'} bg-gradient-to-r from-[#2E7D32] via-emerald-500 to-green-600 hover:from-green-600 hover:via-emerald-600 hover:to-[#2E7D32] text-white rounded-xl font-bold shadow-2xl transition-all flex justify-center items-center transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:ring-offset-2 focus:ring-offset-black/50 ${isMobile ? 'text-base' : 'text-lg'} relative overflow-hidden group min-h-[48px]`}
                      disabled={isLoading}
                    >
                      {/* Button Shine Effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                      {isLoading ? (
                        <span className="flex items-center relative z-10">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {showOtpInput ? 'Verifying OTP...' : 'Sending OTP...'}
                        </span>
                      ) : (
                        <span className="flex items-center gap-3 relative z-10">
                          <MessageCircle className="h-5 w-5" />
                          {showOtpInput ? 'Verify OTP & Create Account' : 'Send SMS OTP'}
                        </span>
                      )}
                    </button>
                  </div>
                </form>

                {/* Sign In Link */}
                <div className="mt-8 text-center border-t border-[#2E7D32]/20 pt-6">
                  <p className="text-gray-400 text-base mb-4">
                    Already have an account?
                  </p>
                  <Link
                    to="/login"
                    className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-black/60 to-[#0F2419]/60 border border-[#2E7D32]/40 text-emerald-300 hover:text-white rounded-xl font-semibold transition-all transform hover:scale-[1.02] hover:border-[#2E7D32]/60 backdrop-blur-sm"
                  >
                    <span>Sign In</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default Register;
