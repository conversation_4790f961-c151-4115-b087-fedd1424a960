import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  MapPin,
  Activity,
  Calendar,
  Clock,
  ChevronDown,
  ChevronUp,
  Zap,
  Star,
  Navigation,
  Loader,
  Check,
  X,
  RefreshCw
} from 'lucide-react';

// Import hooks
import { useSmartBookingDefaults } from '@/hooks/useSmartBookingDefaults';
import { useAvailabilityCache } from '@/utils/availabilityCache';
import { useIsMobile } from '@/hooks/use-mobile';

interface MobileConsolidatedBookingProps {
  onSelectionComplete: (data: {
    venue: string;
    sport: string;
    court: string;
    date: string;
    slots: string[];
    prices: Record<string, number>;
  }) => void;
  initialVenueId?: string;
  initialSportId?: string;
}

interface QuickSlot {
  display: string;
  startTime: string;
  endTime: string;
  price: number;
  isAvailable: boolean;
}

const MobileConsolidatedBooking: React.FC<MobileConsolidatedBookingProps> = ({
  onSelectionComplete,
  initialVenueId,
  initialSportId
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  
  // Hooks
  const { smartDefaults, loading: smartLoading } = useSmartBookingDefaults();
  const { getCachedAvailability, preloadAvailability, getCacheStats } = useAvailabilityCache();

  // State management
  const [selectedVenue, setSelectedVenue] = useState(initialVenueId || '');
  const [selectedSport, setSelectedSport] = useState(initialSportId || '');
  const [selectedCourt, setSelectedCourt] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [selectedSlotPrices, setSelectedSlotPrices] = useState<Record<string, number>>({});
  
  // UI state
  const [activeSection, setActiveSection] = useState<'selection' | 'slots'>('selection');
  const [quickSlots, setQuickSlots] = useState<QuickSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSmartSuggestions, setShowSmartSuggestions] = useState(true);

  // Apply smart defaults when available
  useEffect(() => {
    if (smartDefaults && !smartLoading) {
      if (smartDefaults.venue && !selectedVenue) {
        setSelectedVenue(smartDefaults.venue);
      }
      if (smartDefaults.sport && !selectedSport) {
        setSelectedSport(smartDefaults.sport);
      }
      if (smartDefaults.court && !selectedCourt) {
        setSelectedCourt(smartDefaults.court);
      }
      if (smartDefaults.date && !selectedDate) {
        setSelectedDate(smartDefaults.date);
      }
    }
  }, [smartDefaults, smartLoading, selectedVenue, selectedSport, selectedCourt, selectedDate]);

  // Fetch quick slots when court and date are selected
  const fetchQuickSlots = useCallback(async () => {
    if (!selectedCourt || !selectedDate) return;

    setLoading(true);
    try {
      const slots = await getCachedAvailability(selectedCourt, selectedDate);
      
      const quickSlotData: QuickSlot[] = slots.map(slot => ({
        display: `${slot.start_time} - ${slot.end_time}`,
        startTime: slot.start_time,
        endTime: slot.end_time,
        price: parseFloat(slot.price),
        isAvailable: slot.is_available
      }));

      setQuickSlots(quickSlotData);
      
      // Auto-switch to slots view if selections are complete
      if (selectedVenue && selectedSport && selectedCourt && selectedDate) {
        setActiveSection('slots');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load available slots",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [selectedCourt, selectedDate, getCachedAvailability, toast, selectedVenue, selectedSport]);

  useEffect(() => {
    fetchQuickSlots();
  }, [fetchQuickSlots]);

  // Handle slot selection
  const handleSlotToggle = (slot: QuickSlot) => {
    if (!slot.isAvailable) return;

    const slotDisplay = slot.display;
    const isSelected = selectedSlots.includes(slotDisplay);

    if (isSelected) {
      // Remove slot
      setSelectedSlots(prev => prev.filter(s => s !== slotDisplay));
      setSelectedSlotPrices(prev => {
        const updated = { ...prev };
        delete updated[slotDisplay];
        return updated;
      });
    } else {
      // Add slot
      setSelectedSlots(prev => [...prev, slotDisplay]);
      setSelectedSlotPrices(prev => ({
        ...prev,
        [slotDisplay]: slot.price
      }));
    }
  };

  // Calculate total price
  const totalPrice = Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0);

  // Handle selection completion
  const handleComplete = () => {
    if (!selectedVenue || !selectedSport || !selectedCourt || !selectedDate || selectedSlots.length === 0) {
      toast({
        title: "Incomplete Selection",
        description: "Please complete all selections before proceeding",
        variant: "destructive",
      });
      return;
    }

    onSelectionComplete({
      venue: selectedVenue,
      sport: selectedSport,
      court: selectedCourt,
      date: selectedDate,
      slots: selectedSlots,
      prices: selectedSlotPrices
    });
  };

  // Smart suggestion component
  const SmartSuggestion = ({ type, value, reason, confidence }: {
    type: string;
    value: string;
    reason: string;
    confidence: number;
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-3 mb-3"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Star className="w-4 h-4 text-emerald-400" />
          <span className="text-sm font-medium text-emerald-300">Smart Pick</span>
          <div className="flex">
            {[...Array(Math.ceil(confidence * 5))].map((_, i) => (
              <Star key={i} className="w-3 h-3 text-emerald-400 fill-current" />
            ))}
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowSmartSuggestions(false)}
          className="h-6 w-6 p-0"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
      <p className="text-xs text-gray-300 mt-1">{reason}</p>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Mobile Header */}
      <div className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div className="flex items-center justify-between p-4">
          <h1 className="text-lg font-bold text-white">Quick Booking</h1>
          <div className="flex items-center gap-2">
            {loading && <Loader className="w-4 h-4 animate-spin text-emerald-400" />}
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchQuickSlots}
              className="min-h-[44px] min-w-[44px]"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Section Toggle */}
        <div className="flex bg-gray-800 mx-4 mb-4 rounded-lg p-1">
          <button
            onClick={() => setActiveSection('selection')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all min-h-[44px] ${
              activeSection === 'selection'
                ? 'bg-emerald-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <MapPin className="w-4 h-4" />
              Selection
            </div>
          </button>
          <button
            onClick={() => setActiveSection('slots')}
            disabled={!selectedCourt || !selectedDate}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all min-h-[44px] ${
              activeSection === 'slots'
                ? 'bg-emerald-600 text-white'
                : 'text-gray-400 hover:text-white disabled:opacity-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Clock className="w-4 h-4" />
              Slots ({quickSlots.filter(s => s.isAvailable).length})
            </div>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 pb-24">
        <AnimatePresence mode="wait">
          {activeSection === 'selection' && (
            <motion.div
              key="selection"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {/* Smart Suggestions */}
              {showSmartSuggestions && smartDefaults.venue && smartDefaults.confidence.venue > 0.5 && (
                <SmartSuggestion
                  type="venue"
                  value={smartDefaults.venue}
                  reason={smartDefaults.reasons.venue || ''}
                  confidence={smartDefaults.confidence.venue}
                />
              )}

              {/* Selection Cards */}
              <div className="space-y-3">
                {/* Venue Selection Card */}
                <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                  <div className="flex items-center gap-3 mb-3">
                    <MapPin className="w-5 h-5 text-emerald-400" />
                    <span className="font-medium text-white">Venue</span>
                    {smartDefaults.venue && (
                      <span className="text-xs bg-emerald-900/30 text-emerald-300 px-2 py-1 rounded">
                        Auto-selected
                      </span>
                    )}
                  </div>
                  {/* Venue selector implementation */}
                  <div className="text-sm text-gray-400">
                    Venue selection component will be implemented here
                  </div>
                </div>

                {/* Sport Selection Card */}
                <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                  <div className="flex items-center gap-3 mb-3">
                    <Activity className="w-5 h-5 text-emerald-400" />
                    <span className="font-medium text-white">Sport</span>
                    {smartDefaults.sport && (
                      <span className="text-xs bg-emerald-900/30 text-emerald-300 px-2 py-1 rounded">
                        Your favorite
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-400">
                    Sport selection component will be implemented here
                  </div>
                </div>

                {/* Date & Court Combined Card */}
                <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                  <div className="flex items-center gap-3 mb-3">
                    <Calendar className="w-5 h-5 text-emerald-400" />
                    <span className="font-medium text-white">Date & Court</span>
                  </div>
                  <div className="text-sm text-gray-400">
                    Date and court selection components will be implemented here
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeSection === 'slots' && (
            <motion.div
              key="slots"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-4"
            >
              {/* Quick Slots Grid */}
              <div className="grid grid-cols-2 gap-3">
                {quickSlots.map((slot, index) => {
                  const isSelected = selectedSlots.includes(slot.display);
                  const isAvailable = slot.isAvailable;
                  
                  return (
                    <motion.button
                      key={slot.display}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => handleSlotToggle(slot)}
                      disabled={!isAvailable}
                      className={`
                        p-4 rounded-xl border-2 transition-all min-h-[80px] touch-manipulation
                        ${isSelected
                          ? 'bg-emerald-600 border-emerald-500 text-white'
                          : isAvailable
                          ? 'bg-gray-800 border-gray-600 text-white hover:border-emerald-500'
                          : 'bg-gray-800/50 border-gray-700 text-gray-500 cursor-not-allowed'
                        }
                      `}
                    >
                      <div className="flex flex-col items-center gap-1">
                        <div className="text-sm font-medium">
                          {slot.startTime} - {slot.endTime}
                        </div>
                        <div className="text-lg font-bold">
                          ₹{slot.price}
                        </div>
                        {isSelected && (
                          <Check className="w-4 h-4 text-white" />
                        )}
                        {!isAvailable && (
                          <X className="w-4 h-4 text-gray-500" />
                        )}
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              {quickSlots.length === 0 && !loading && (
                <div className="text-center py-12 text-gray-400">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No slots available for selected date</p>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Fixed Bottom Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-t border-gray-800 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-white">
            <div className="text-sm text-gray-400">Total</div>
            <div className="text-2xl font-bold text-emerald-400">₹{totalPrice}</div>
          </div>
          <div className="text-right text-sm text-gray-400">
            {selectedSlots.length} slot{selectedSlots.length !== 1 ? 's' : ''} selected
          </div>
        </div>
        
        <Button
          onClick={handleComplete}
          disabled={selectedSlots.length === 0 || loading}
          className="w-full bg-emerald-600 hover:bg-emerald-700 text-white min-h-[48px] text-lg font-semibold"
        >
          {loading ? (
            <Loader className="w-5 h-5 animate-spin" />
          ) : (
            `Continue to Payment - ₹${totalPrice}`
          )}
        </Button>
      </div>
    </div>
  );
};

export default MobileConsolidatedBooking;
