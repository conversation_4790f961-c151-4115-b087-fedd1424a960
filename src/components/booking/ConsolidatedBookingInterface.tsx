import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  MapPin,
  Activity,
  Calendar,
  Clock,
  ChevronDown,
  ChevronUp,
  Loader,
  RefreshCw,
  Zap
} from 'lucide-react';

// Import existing utilities
import {
  getISTDateString,
  getMinBookingDate,
  getMaxBookingDate,
  validateBookingDate
} from '@/utils/timezone';

interface ConsolidatedBookingInterfaceProps {
  onSlotSelection: (slots: string[], prices: Record<string, number>) => void;
  onSelectionComplete: (data: {
    venue: string;
    sport: string;
    court: string;
    date: string;
    slots: string[];
    prices: Record<string, number>;
  }) => void;
  initialVenueId?: string;
  initialSportId?: string;
  initialCourtId?: string;
  initialDate?: string;
}

interface SmartDefaults {
  venue?: string;
  sport?: string;
  court?: string;
  date?: string;
}

interface AvailabilityCache {
  [key: string]: {
    data: any[];
    timestamp: number;
    expires: number;
  };
}

const ConsolidatedBookingInterface: React.FC<ConsolidatedBookingInterfaceProps> = ({
  onSlotSelection,
  onSelectionComplete,
  initialVenueId,
  initialSportId,
  initialCourtId,
  initialDate
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // State management
  const [venues, setVenues] = useState<any[]>([]);
  const [sports, setSports] = useState<any[]>([]);
  const [courts, setCourts] = useState<any[]>([]);
  const [selectedVenue, setSelectedVenue] = useState(initialVenueId || '');
  const [selectedSport, setSelectedSport] = useState(initialSportId || '');
  const [selectedCourt, setSelectedCourt] = useState(initialCourtId || '');
  const [selectedDate, setSelectedDate] = useState(initialDate || getISTDateString());
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [selectedSlotPrices, setSelectedSlotPrices] = useState<Record<string, number>>({});
  const [availableTimeSlots, setAvailableTimeSlots] = useState<any[]>([]);

  // UI state
  const [loading, setLoading] = useState({
    venues: false,
    sports: false,
    courts: false,
    availability: false,
    smartDefaults: false
  });
  const [isSelectionPanelCollapsed, setIsSelectionPanelCollapsed] = useState(false);
  const [smartDefaults, setSmartDefaults] = useState<SmartDefaults>({});

  // Performance optimization
  const availabilityCache = useRef<AvailabilityCache>({});
  const lastAvailabilityCall = useRef<string>('');
  const CACHE_DURATION = 30000; // 30 seconds

  // Smart defaults hook
  const useSmartDefaults = useCallback(async () => {
    if (!user) return;

    setLoading(prev => ({ ...prev, smartDefaults: true }));
    
    try {
      // Get user's location preference from profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('preferred_location, favorite_sport')
        .eq('id', user.id)
        .single();

      // Get user's recent bookings for patterns
      const { data: recentBookings } = await supabase
        .from('bookings')
        .select(`
          venue_id,
          court:courts(sport_id, venue_id),
          booking_date
        `)
        .eq('user_id', user.id)
        .eq('status', 'confirmed')
        .order('created_at', { ascending: false })
        .limit(5);

      const defaults: SmartDefaults = {};

      // Auto-select favorite sport
      if (profile?.favorite_sport) {
        defaults.sport = profile.favorite_sport;
      } else if (recentBookings?.length > 0) {
        // Use most frequent sport from recent bookings
        const sportCounts = recentBookings.reduce((acc, booking) => {
          const sportId = booking.court?.sport_id;
          if (sportId) acc[sportId] = (acc[sportId] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        const mostFrequentSport = Object.entries(sportCounts)
          .sort(([,a], [,b]) => b - a)[0]?.[0];
        
        if (mostFrequentSport) defaults.sport = mostFrequentSport;
      }

      // Auto-select most frequent venue
      if (recentBookings?.length > 0) {
        const venueCounts = recentBookings.reduce((acc, booking) => {
          const venueId = booking.court?.venue_id;
          if (venueId) acc[venueId] = (acc[venueId] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        const mostFrequentVenue = Object.entries(venueCounts)
          .sort(([,a], [,b]) => b - a)[0]?.[0];
        
        if (mostFrequentVenue) defaults.venue = mostFrequentVenue;
      }

      // Auto-select next available date (today or tomorrow)
      const today = getISTDateString();
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStr = tomorrow.toISOString().split('T')[0];
      
      // Check if today is within booking window
      const todayValidation = validateBookingDate(today);
      defaults.date = todayValidation.valid ? today : tomorrowStr;

      setSmartDefaults(defaults);

      // Apply smart defaults if no initial values provided
      if (!initialVenueId && defaults.venue) setSelectedVenue(defaults.venue);
      if (!initialSportId && defaults.sport) setSelectedSport(defaults.sport);
      if (!initialDate && defaults.date) setSelectedDate(defaults.date);

    } catch (error) {
      console.error('Error loading smart defaults:', error);
    } finally {
      setLoading(prev => ({ ...prev, smartDefaults: false }));
    }
  }, [user, initialVenueId, initialSportId, initialDate]);

  // Cached availability fetching
  const getCachedAvailability = useCallback(async (courtId: string, date: string) => {
    const cacheKey = `${courtId}-${date}`;
    const cached = availabilityCache.current[cacheKey];
    
    // Return cached data if still valid
    if (cached && Date.now() < cached.expires) {
      return cached.data;
    }

    // Prevent duplicate calls
    if (lastAvailabilityCall.current === cacheKey) {
      return availableTimeSlots;
    }
    
    lastAvailabilityCall.current = cacheKey;

    try {
      const { data, error } = await supabase.rpc('get_unified_availability', {
        p_court_id: courtId,
        p_date: date
      });

      if (error) throw error;

      const slotsWithPrice = (data || []).map((slot: any) => ({
        ...slot,
        price: slot.price || '0',
        booking_type: slot.booking_type || 'court_based'
      }));

      // Cache the result
      availabilityCache.current[cacheKey] = {
        data: slotsWithPrice,
        timestamp: Date.now(),
        expires: Date.now() + CACHE_DURATION
      };

      return slotsWithPrice;
    } catch (error) {
      console.error('Error fetching availability:', error);
      throw error;
    }
  }, [availableTimeSlots]);

  // Optimized availability fetching
  const fetchAvailability = useCallback(async () => {
    if (!selectedCourt || !selectedDate) return;

    setLoading(prev => ({ ...prev, availability: true }));
    
    try {
      const slots = await getCachedAvailability(selectedCourt, selectedDate);
      setAvailableTimeSlots(slots);
      
      // Clear invalid selected slots
      const validSlots = selectedSlots.filter(slotDisplay => {
        const [startTime, endTime] = slotDisplay.split(' - ');
        return slots.some((slot: any) => 
          slot.start_time === startTime && 
          slot.end_time === endTime && 
          slot.is_available
        );
      });
      
      if (validSlots.length !== selectedSlots.length) {
        setSelectedSlots(validSlots);
        const validPrices = validSlots.reduce((acc, slot) => {
          if (selectedSlotPrices[slot]) {
            acc[slot] = selectedSlotPrices[slot];
          }
          return acc;
        }, {} as Record<string, number>);
        setSelectedSlotPrices(validPrices);
      }
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load availability",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, availability: false }));
    }
  }, [selectedCourt, selectedDate, selectedSlots, selectedSlotPrices, getCachedAvailability, toast]);

  // Initialize smart defaults
  useEffect(() => {
    useSmartDefaults();
  }, [useSmartDefaults]);

  // Fetch availability when court/date changes
  useEffect(() => {
    if (selectedCourt && selectedDate) {
      fetchAvailability();
    }
  }, [selectedCourt, selectedDate, fetchAvailability]);

  // Notify parent of selection changes
  useEffect(() => {
    onSlotSelection(selectedSlots, selectedSlotPrices);
  }, [selectedSlots, selectedSlotPrices, onSlotSelection]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
      {/* Selection Panel - Collapsible on Mobile */}
      <div className="lg:col-span-1">
        <div className="bg-gray-800 rounded-xl border border-emerald-800/30">
          {/* Panel Header */}
          <div 
            className="flex items-center justify-between p-4 cursor-pointer lg:cursor-default"
            onClick={() => setIsSelectionPanelCollapsed(!isSelectionPanelCollapsed)}
          >
            <h3 className="font-semibold text-white flex items-center gap-2">
              <Zap className="w-5 h-5 text-emerald-400" />
              Quick Selection
            </h3>
            <div className="lg:hidden">
              {isSelectionPanelCollapsed ? <ChevronDown size={20} /> : <ChevronUp size={20} />}
            </div>
          </div>

          {/* Panel Content */}
          <AnimatePresence>
            {(!isSelectionPanelCollapsed || window.innerWidth >= 1024) && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="px-4 pb-4 space-y-4"
              >
                {/* Smart Defaults Indicator */}
                {loading.smartDefaults && (
                  <div className="flex items-center gap-2 text-sm text-emerald-400">
                    <Loader className="w-4 h-4 animate-spin" />
                    Loading your preferences...
                  </div>
                )}

                {/* Venue Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <MapPin size={16} className="text-emerald-400" />
                    Venue
                    {smartDefaults.venue && (
                      <span className="text-xs bg-emerald-900/30 text-emerald-300 px-2 py-1 rounded">
                        Auto-selected
                      </span>
                    )}
                  </label>
                  {/* Venue selector implementation */}
                </div>

                {/* Sport Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Activity size={16} className="text-emerald-400" />
                    Sport
                    {smartDefaults.sport && (
                      <span className="text-xs bg-emerald-900/30 text-emerald-300 px-2 py-1 rounded">
                        Your favorite
                      </span>
                    )}
                  </label>
                  {/* Sport selector implementation */}
                </div>

                {/* Date Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Calendar size={16} className="text-emerald-400" />
                    Date
                    {smartDefaults.date && (
                      <span className="text-xs bg-emerald-900/30 text-emerald-300 px-2 py-1 rounded">
                        Next available
                      </span>
                    )}
                  </label>
                  {/* Date picker implementation */}
                </div>

                {/* Court Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Clock size={16} className="text-emerald-400" />
                    Court
                  </label>
                  {/* Court selector implementation */}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Slot Grid Panel */}
      <div className="lg:col-span-2">
        <div className="bg-gray-800 rounded-xl border border-emerald-800/30 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-white">Available Slots</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchAvailability}
              disabled={loading.availability}
              className="min-h-[44px]"
            >
              {loading.availability ? (
                <Loader className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Slot Grid Implementation */}
          {selectedCourt && selectedDate ? (
            <div className="space-y-4">
              {/* Slot grid will be implemented here */}
              <div className="text-center text-gray-400">
                Slot grid implementation pending...
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-400">
              <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Select venue, sport, court and date to view available slots</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConsolidatedBookingInterface;
