import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock, User } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import SlotBlockingForm from '@/components/admin/SlotBlockingForm';
import { motion } from 'framer-motion';

interface TimeSlot {
  start_time: string;
  end_time: string;
  price: string;
  is_available: boolean;
  booking_type: 'court_based' | 'capacity_based';
  available_spots?: number;
  max_capacity?: number;
}
interface SlotBlockingTabProps {
  userRole: string | null;
  adminVenues: {
    venue_id: string;
  }[];
}
const SlotBlockingTab: React.FC<SlotBlockingTabProps> = ({
  userRole,
  adminVenues
}) => {
  const [venues, setVenues] = useState<{
    id: string;
    name: string;
  }[]>([]);
  const [selectedVenueId, setSelectedVenueId] = useState<string>('');
  const [selectedVenueName, setSelectedVenueName] = useState<string>('');
  const [courts, setCourts] = useState<{
    id: string;
    name: string;
  }[]>([]);
  const [selectedCourtId, setSelectedCourtId] = useState<string>('');
  const [selectedCourtName, setSelectedCourtName] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());

  // Time utility functions
  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const parseFormattedTime = (formattedTime: string) => {
    const [time, ampm] = formattedTime.split(' ');
    const [hours, minutes] = time.split(':');
    let hour = parseInt(hours, 10);

    if (ampm === 'PM' && hour !== 12) {
      hour += 12;
    } else if (ampm === 'AM' && hour === 12) {
      hour = 0;
    }

    return `${hour.toString().padStart(2, '0')}:${minutes}`;
  };

  // Enhanced slot selection logic for blocking (allows non-consecutive slots)
  const handleSlotClick = (slot: TimeSlot) => {
    if (!slot.is_available) return;

    const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;

    if (selectedSlots.includes(slotDisplay)) {
      // Remove slot if already selected
      setSelectedSlots(selectedSlots.filter(s => s !== slotDisplay));
    } else {
      // Add slot to selection (allows non-consecutive for blocking)
      setSelectedSlots([...selectedSlots, slotDisplay]);
    }
  };

  // Fetch availability for selected court and date
  const fetchAvailability = useCallback(async () => {
    if (!selectedCourtId || !selectedDate) return;

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_unified_availability' as any, {
        p_court_id: selectedCourtId,
        p_date: format(selectedDate, 'yyyy-MM-dd')
      });

      if (error) throw error;

      const slotsWithPrice = (data as any)?.map((slot: {
        start_time: string;
        end_time: string;
        is_available: boolean;
        available_spots: number;
        max_capacity: number;
        price: string;
        booking_type: string;
      }) => ({
        ...slot,
        price: slot.price || '0',
        booking_type: slot.booking_type || 'court_based'
      })) || [];

      setAvailableTimeSlots(slotsWithPrice);
    } catch (error) {
      // Error fetching availability - details removed for production security
      toast({
        title: "Error",
        description: "Failed to load availability",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [selectedCourtId, selectedDate]);

  // Fetch venues based on user role
  useEffect(() => {
    const fetchVenues = async () => {
      try {
        setLoading(true);
        let data;
        if (userRole === 'admin') {
          // For admin users, only show venues they manage
          if (adminVenues.length > 0) {
            const {
              data: venuesData,
              error: venuesError
            } = await supabase.from('venues').select('id, name').in('id', adminVenues.map(v => v.venue_id)).eq('is_active', true);
            if (venuesError) throw venuesError;
            data = venuesData;
          } else {
            data = [];
          }
        } else if (userRole === 'super_admin') {
          // For super_admin, show all venues
          const {
            data: venuesData,
            error: venuesError
          } = await supabase.from('venues').select('id, name').eq('is_active', true);
          if (venuesError) throw venuesError;
          data = venuesData;
        }
        if (data && data.length > 0) {
          setVenues(data);
          setSelectedVenueId(data[0].id);
          setSelectedVenueName(data[0].name);
        }
      } catch (error) {
        console.error('Error fetching venues:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch venues',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };
    if (userRole === 'admin' || userRole === 'super_admin') {
      fetchVenues();
    }
  }, [userRole, adminVenues]);

  // Fetch courts when venue is selected
  useEffect(() => {
    const fetchCourts = async () => {
      if (!selectedVenueId) return;
      try {
        setLoading(true);
        const {
          data,
          error
        } = await supabase.from('courts').select('id, name').eq('venue_id', selectedVenueId).eq('is_active', true);
        if (error) throw error;
        if (data && data.length > 0) {
          setCourts(data);
          setSelectedCourtId(data[0].id);
          setSelectedCourtName(data[0].name);
        } else {
          setCourts([]);
          setSelectedCourtId('');
          setSelectedCourtName('');
        }
      } catch (error) {
        console.error('Error fetching courts:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch courts for this venue',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };
    fetchCourts();
  }, [selectedVenueId]);

  // Fetch availability when court or date changes
  useEffect(() => {
    if (selectedCourtId && selectedDate) {
      fetchAvailability();
    }
  }, [selectedCourtId, selectedDate, lastRefresh, fetchAvailability]);

  // Handle court selection
  const handleCourtSelect = (courtId: string) => {
    const court = courts.find(c => c.id === courtId);
    if (court) {
      setSelectedCourtId(courtId);
      setSelectedCourtName(court.name);
      setSelectedSlots([]); // Reset selected slots when changing courts
    }
  };

  // Handle blocking completion
  const handleBlockComplete = () => {
    setSelectedSlots([]);
    setLastRefresh(Date.now()); // Force refresh after blocking
    fetchAvailability(); // Refresh availability
    toast({
      title: 'Success',
      description: 'Time slots blocked successfully',
      variant: 'default'
    });
  };

  // Handle manual refresh
  const handleManualRefresh = () => {
    setLastRefresh(Date.now());
  };
  if (loading && venues.length === 0) {
    return <div className="flex justify-center items-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>;
  }
  return <div className="space-y-4">
      {/* Venue Selection */}
      {venues.length > 0 && <div className="mb-4">
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Select Venue
          </label>
          <Select value={selectedVenueId} onValueChange={value => {
        setSelectedVenueId(value);
        const venue = venues.find(v => v.id === value);
        if (venue) setSelectedVenueName(venue.name);
      }}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a venue" />
            </SelectTrigger>
            <SelectContent>
              {venues.map(venue => <SelectItem key={venue.id} value={venue.id}>{venue.name}</SelectItem>)}
            </SelectContent>
          </Select>
        </div>}
      
      {/* Date Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
          Select Date
        </label>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full justify-start">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {format(selectedDate, 'PPP')}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar mode="single" selected={selectedDate} onSelect={date => {
            if (date) {
              setSelectedDate(date);
              setSelectedSlots([]); // Reset selected slots when date changes
            }
          }} initialFocus />
          </PopoverContent>
        </Popover>
      </div>
      
      {/* Court Selection */}
      {courts.length > 0 && <div className="mb-4">
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            Select Court
          </label>
          <Select value={selectedCourtId} onValueChange={handleCourtSelect}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a court" />
            </SelectTrigger>
            <SelectContent>
              {courts.map(court => <SelectItem key={court.id} value={court.id}>{court.name}</SelectItem>)}
            </SelectContent>
          </Select>
        </div>}
      
      {/* Manual refresh button */}
      <Button variant="outline" size="sm" onClick={handleManualRefresh} className="mb-4 w-full">
        Refresh Availability
      </Button>
      
      {/* Enhanced Slot Selection UI */}
      {selectedCourtId ? (
        <div className="dark:bg-navy-800 rounded-lg shadow p-4 mb-6 bg-emerald-900">
          <h3 className="text-md font-medium mb-3 text-gray-900 dark:text-white">Available Time Slots</h3>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Clock className="h-8 w-8 animate-spin text-emerald-400" />
              <span className="ml-2 text-gray-400">Loading slots...</span>
            </div>
          ) : availableTimeSlots.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No slots available for this date</p>
            </div>
          ) : (
            <>
              {/* Multiple Slot Selection Info */}
              <div className="mb-4 p-3 bg-orange-900/20 rounded-lg border border-orange-800/30">
                <div className="flex items-start gap-2">
                  <div className="w-4 h-4 bg-orange-500 rounded-full mt-0.5 flex-shrink-0"></div>
                  <div>
                    <h4 className="text-sm font-medium text-orange-300 mb-1">Multiple Slot Selection</h4>
                    <p className="text-xs text-gray-400 mb-2">
                      You can select multiple time slots for blocking. Non-consecutive slots are allowed.
                    </p>
                    <p className="text-xs text-orange-300">
                      💡 <strong>Tip:</strong> Select all slots you want to block at once
                    </p>
                  </div>
                </div>
              </div>

              {/* Slot Grid */}
              <div className="grid grid-cols-1 gap-3">
                {availableTimeSlots.map((slot, index) => {
                  const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
                  const isSelected = selectedSlots.includes(slotDisplay);

                  return (
                    <motion.button
                      key={`${slot.start_time}-${slot.end_time}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      whileHover={{ scale: slot.is_available ? 1.02 : 1 }}
                      whileTap={{ scale: slot.is_available ? 0.98 : 1 }}
                      disabled={!slot.is_available}
                      onClick={() => handleSlotClick(slot)}
                      className={`
                        relative p-4 rounded-xl border-2 transition-all duration-200 text-left
                        min-h-[80px] flex flex-col justify-between
                        ${!slot.is_available
                          ? 'bg-red-900/20 border-red-800/50 text-red-300 cursor-not-allowed'
                          : isSelected
                            ? 'bg-orange-600/20 border-orange-500 text-orange-100 shadow-lg shadow-orange-900/20'
                            : 'bg-gray-800/50 border-gray-600/50 hover:border-orange-500/50 hover:bg-gray-750/50 text-gray-200'
                        }
                        ${isSelected ? 'ring-2 ring-orange-400/50' : ''}
                      `}
                    >
                      {/* Time and Price Row */}
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-semibold text-base">
                            {formatTime(slot.start_time)}
                          </div>
                          <div className="text-sm opacity-75">
                            to {formatTime(slot.end_time)}
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="font-bold text-base">
                            ₹{parseFloat(slot.price).toFixed(0)}
                          </div>
                          {slot.booking_type === 'capacity_based' && (
                            <div className="text-xs opacity-75 flex items-center gap-1">
                              <User size={10} />
                              {slot.available_spots || 0}/{slot.max_capacity || 0}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Status Indicators */}
                      <div className="flex justify-between items-center mt-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full transition-all ${
                            isSelected
                              ? 'bg-orange-400 shadow-lg shadow-orange-400/50'
                              : slot.is_available
                                ? 'bg-gray-600 border border-gray-500'
                                : 'bg-red-600'
                          }`} />

                          <span className="text-xs font-medium">
                            {!slot.is_available
                              ? 'Booked'
                              : isSelected
                                ? 'Selected for blocking'
                                : 'Available'
                            }
                          </span>
                        </div>

                        {slot.booking_type === 'capacity_based' && (
                          <div className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded-full">
                            Capacity
                          </div>
                        )}
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              {/* Selected Slots Summary */}
              {selectedSlots.length > 0 && (
                <div className="mt-4 p-3 bg-orange-900/20 rounded-lg border border-orange-700/30">
                  <h4 className="text-sm font-medium text-orange-300 mb-2">Selected Slots for Blocking</h4>
                  <div className="space-y-1">
                    {selectedSlots.map(slot => (
                      <div key={slot} className="flex justify-between text-xs">
                        <span className="text-gray-300">{slot}</span>
                        <span className="text-orange-300">Will be blocked</span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-2 pt-2 border-t border-orange-700/30 text-sm font-medium text-orange-300">
                    Total: {selectedSlots.length} slot{selectedSlots.length > 1 ? 's' : ''} selected
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      ) : (
        <div className="bg-white dark:bg-navy-800 rounded-lg shadow p-4 mb-6">
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">Select a court to view availability</p>
        </div>
      )}
      
      {/* Slot Blocking Form */}
      {selectedCourtId && selectedSlots.length > 0 ? (
        <div className="bg-white dark:bg-navy-800 rounded-lg shadow p-4">
          <SlotBlockingForm
            courtId={selectedCourtId}
            courtName={selectedCourtName}
            date={format(selectedDate, 'yyyy-MM-dd')}
            selectedSlots={selectedSlots.map(slot => ({
              start_time: parseFormattedTime(slot.split(' - ')[0]),
              end_time: parseFormattedTime(slot.split(' - ')[1]),
              is_available: true
            }))}
            onBlockComplete={handleBlockComplete}
          />
        </div>
      ) : (
        <div className="bg-white dark:bg-navy-800 rounded-lg shadow p-4">
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            Select time slots to block
          </p>
        </div>
      )}
    </div>;
};
export default SlotBlockingTab;