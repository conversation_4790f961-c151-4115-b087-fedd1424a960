import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Venue {
  id: string;
  name: string;
}

interface VenueSelectorProps {
  venues: Venue[];
  selectedVenueId: string;
  onVenueChange: (venueId: string) => void;
  userRole: string | null;
  showAllOption?: boolean;
  className?: string;
  placeholder?: string;
  label?: string;
  variant?: 'mobile' | 'desktop';
}

const VenueSelector: React.FC<VenueSelectorProps> = ({
  venues,
  selectedVenueId,
  onVenueChange,
  userRole,
  showAllOption = true,
  className = '',
  placeholder = 'Select venue',
  label = 'Select Venue',
  variant = 'desktop'
}) => {
  const isMobile = variant === 'mobile';
  
  // Show "All Venues" option for super admins or when explicitly requested
  const shouldShowAllOption = showAllOption && (userRole === 'super_admin' || venues.length > 1);

  const baseSelectClasses = isMobile
    ? 'bg-black border-navy-700 text-white'
    : 'bg-white border-gray-300 text-gray-900';

  const labelClasses = isMobile
    ? 'text-white text-sm font-medium'
    : 'text-gray-700 text-sm font-medium';

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className={`block ${labelClasses}`}>
          {label}
        </label>
      )}
      <Select value={selectedVenueId} onValueChange={onVenueChange}>
        <SelectTrigger className={`w-full ${baseSelectClasses}`}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className={isMobile ? 'bg-black border-navy-700 text-white' : 'bg-white border-gray-300'}>
          {shouldShowAllOption && (
            <SelectItem
              value="all"
              className={isMobile ? 'text-white hover:bg-gray-800' : 'text-gray-900 hover:bg-gray-100'}
            >
              All Venues
            </SelectItem>
          )}
          {venues.map(venue => (
            <SelectItem
              key={venue.id}
              value={venue.id}
              className={isMobile ? 'text-white hover:bg-gray-800' : 'text-gray-900 hover:bg-gray-100'}
            >
              {venue.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default VenueSelector;
