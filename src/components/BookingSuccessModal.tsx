import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  Calendar, 
  Download, 
  Clock, 
  MapPin, 
  User, 
  CreditCard,
  X,
  ExternalLink
} from 'lucide-react';

interface BookingDetails {
  booking_reference: string;
  guest_name: string;
  guest_phone: string;
  venue_name: string;
  venue_location: string;
  court_name: string;
  sport_name: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: string;
  payment_reference?: string;
}

interface BookingSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  bookingDetails: BookingDetails;
  onContinue?: () => void;
}

const BookingSuccessModal: React.FC<BookingSuccessModalProps> = ({
  isOpen,
  onClose,
  bookingDetails,
  onContinue
}) => {
  const [showCalendarOptions, setShowCalendarOptions] = useState(false);

  // Booking reference verification logging removed for production security

  // Format time for display - handles both 24-hour (HH:MM) and 12-hour (HH:MM AM/PM) formats
  const formatTime = (time: string) => {
    // Check if time already contains AM/PM
    if (time.includes('AM') || time.includes('PM')) {
      return time; // Already formatted, return as-is
    }

    // Handle 24-hour format (HH:MM or HH:MM:SS)
    const timeParts = time.split(':');
    if (timeParts.length < 2) {
      return time; // Invalid format, return as-is
    }

    const hours = parseInt(timeParts[0]);
    const minutes = timeParts[1];

    // Convert to 12-hour format
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHour = hours % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  // Format date for display
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-IN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Convert time to 24-hour format for calendar URL generation
  const convertTo24Hour = (time: string) => {
    // If already in 24-hour format (no AM/PM), return as-is
    if (!time.includes('AM') && !time.includes('PM')) {
      return time.length === 5 ? `${time}:00` : time;
    }

    // Parse 12-hour format
    const [timePart, period] = time.split(' ');
    const [hours, minutes] = timePart.split(':');
    let hour24 = parseInt(hours);

    if (period === 'PM' && hour24 !== 12) {
      hour24 += 12;
    } else if (period === 'AM' && hour24 === 12) {
      hour24 = 0;
    }

    return `${hour24.toString().padStart(2, '0')}:${minutes}:00`;
  };

  // Generate calendar URLs
  const generateCalendarUrls = () => {
    const title = encodeURIComponent(`${bookingDetails.sport_name} Booking - ${bookingDetails.venue_name}`);
    const location = encodeURIComponent(`${bookingDetails.venue_name}, ${bookingDetails.venue_location}`);
    const details = encodeURIComponent([
      `🏈 ${bookingDetails.sport_name} at ${bookingDetails.court_name}`,
      `🎫 Booking: ${bookingDetails.booking_reference}`,
      `👤 Player: ${bookingDetails.guest_name}`,
      `📞 Contact: ${bookingDetails.guest_phone}`,
      `💰 Amount: ₹${bookingDetails.total_price}`,
      `💳 Payment ID: ${bookingDetails.payment_reference}`,
      ``,
      `⚡ Powered by Grid२Play - India's Premier Sports Booking Platform`
    ].join('\n'));

    // Safely convert to ISO format for URLs with proper error handling
    let startISO: string, endISO: string;

    try {
      // Ensure proper date format (YYYY-MM-DD) and time format (HH:MM or HH:MM:SS)
      const dateStr = bookingDetails.booking_date.includes('T')
        ? bookingDetails.booking_date.split('T')[0]
        : bookingDetails.booking_date;

      // Convert times to 24-hour format for proper Date constructor usage
      const startTimeStr = convertTo24Hour(bookingDetails.start_time);
      const endTimeStr = convertTo24Hour(bookingDetails.end_time);

      const startDate = new Date(`${dateStr}T${startTimeStr}+05:30`);
      const endDate = new Date(`${dateStr}T${endTimeStr}+05:30`);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('Invalid date/time format');
      }

      startISO = startDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
      endISO = endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    } catch (error) {
      // Date conversion error - error details removed for production security
      // Fallback to current time + 1 hour
      const now = new Date();
      const later = new Date(now.getTime() + 60 * 60 * 1000);
      startISO = now.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
      endISO = later.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    }

    return {
      google: `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startISO}/${endISO}&location=${location}&details=${details}`,
      outlook: `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`,
      yahoo: `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${title}&st=${startISO}&et=${endISO}&in_loc=${location}&desc=${details}`,
      office365: `https://outlook.office.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`
    };
  };

  // Generate .ics file content
  const generateICSContent = () => {
    // Safely format date and time for ICS
    const dateStr = bookingDetails.booking_date.includes('T')
      ? bookingDetails.booking_date.split('T')[0]
      : bookingDetails.booking_date;

    // Convert times to 24-hour format for proper ICS formatting
    const startTimeStr = convertTo24Hour(bookingDetails.start_time);
    const endTimeStr = convertTo24Hour(bookingDetails.end_time);

    const startDateTime = dateStr.replace(/-/g, '') + 'T' + startTimeStr.replace(/:/g, '').substring(0, 6);
    const endDateTime = dateStr.replace(/-/g, '') + 'T' + endTimeStr.replace(/:/g, '').substring(0, 6);
    const createdDateTime = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

    const description = [
      `🏈 ${bookingDetails.sport_name} Booking at ${bookingDetails.venue_name}`,
      `🎫 Booking Reference: ${bookingDetails.booking_reference}`,
      `👤 Player: ${bookingDetails.guest_name}`,
      `📞 Contact: ${bookingDetails.guest_phone}`,
      `🏟️ Court: ${bookingDetails.court_name}`,
      `💰 Amount Paid: ₹${bookingDetails.total_price}`,
      `💳 Payment ID: ${bookingDetails.payment_reference}`,
      ``,
      `📍 Venue Address:`,
      `${bookingDetails.venue_location}`,
      ``,
      `⚡ Powered by Grid२Play - India's Premier Sports Booking Platform`
    ].join('\\n');

    return [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Grid2Play//Sports Booking Calendar//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:PUBLISH',
      'BEGIN:VEVENT',
      `UID:${bookingDetails.booking_reference}@grid2play.com`,
      `DTSTAMP:${createdDateTime}`,
      `DTSTART;TZID=Asia/Kolkata:${startDateTime}`,
      `DTEND;TZID=Asia/Kolkata:${endDateTime}`,
      `SUMMARY:${bookingDetails.sport_name} Booking - ${bookingDetails.venue_name}`,
      `DESCRIPTION:${description}`,
      `LOCATION:${bookingDetails.venue_name}, ${bookingDetails.venue_location}`,
      `STATUS:CONFIRMED`,
      `TRANSP:OPAQUE`,
      `CATEGORIES:SPORTS,BOOKING,GRID2PLAY`,
      'BEGIN:VALARM',
      'TRIGGER:-PT1H',
      'ACTION:DISPLAY',
      'DESCRIPTION:Your sports booking starts in 1 hour!',
      'END:VALARM',
      'BEGIN:VALARM',
      'TRIGGER:-PT15M',
      'ACTION:DISPLAY',
      'DESCRIPTION:Your sports booking starts in 15 minutes!',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');
  };

  // Download .ics file
  const downloadICSFile = () => {
    const icsContent = generateICSContent();
    const blob = new Blob([icsContent], { type: 'text/calendar' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${bookingDetails.booking_reference}.ics`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  // Handle calendar platform click
  const handleCalendarClick = (_platform: string, url: string) => {
    window.open(url, '_blank');
  };

  if (!isOpen || !bookingDetails) return null;

  const calendarUrls = generateCalendarUrls();

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="bg-gray-900 rounded-2xl border border-emerald-800/30 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="p-6 border-b border-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-emerald-600 rounded-full">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">Booking Confirmed!</h2>
                    <p className="text-gray-400">Your sports booking is confirmed and paid</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
                >
                  <X className="h-5 w-5 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Booking Details */}
            <div className="p-6">
              <div className="bg-gray-800 rounded-xl p-4 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4">📋 Booking Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">{formatDate(bookingDetails.booking_date)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">{formatTime(bookingDetails.start_time)} - {formatTime(bookingDetails.end_time)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">{bookingDetails.venue_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">{bookingDetails.guest_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300">₹{bookingDetails.total_price} PAID</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs bg-emerald-100 text-emerald-800 px-2 py-1 rounded">
                      {bookingDetails.sport_name} - {bookingDetails.court_name}
                    </span>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-700">
                  <div className="text-xs text-gray-400">
                    <strong>Booking Reference:</strong> {bookingDetails.booking_reference}
                  </div>
                </div>
              </div>

              {/* Calendar Integration */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-emerald-400" />
                    Add to Your Calendar
                  </h3>
                  <button
                    onClick={() => setShowCalendarOptions(!showCalendarOptions)}
                    className="text-emerald-400 hover:text-emerald-300 text-sm flex items-center gap-1"
                  >
                    {showCalendarOptions ? 'Hide Options' : 'Show Options'}
                    <ExternalLink className="h-4 w-4" />
                  </button>
                </div>

                <p className="text-gray-400 text-sm">
                  Never miss your sports booking! Add this event to your calendar with automatic reminders.
                </p>

                <AnimatePresence>
                  {showCalendarOptions && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-3"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <button
                          onClick={() => handleCalendarClick('google', calendarUrls.google)}
                          className="flex items-center justify-center gap-2 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          <Calendar className="h-4 w-4" />
                          Google Calendar
                        </button>

                        <button
                          onClick={() => handleCalendarClick('outlook', calendarUrls.outlook)}
                          className="flex items-center justify-center gap-2 bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors"
                        >
                          <Calendar className="h-4 w-4" />
                          Outlook
                        </button>

                        <button
                          onClick={() => handleCalendarClick('yahoo', calendarUrls.yahoo)}
                          className="flex items-center justify-center gap-2 bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
                        >
                          <Calendar className="h-4 w-4" />
                          Yahoo Calendar
                        </button>

                        <button
                          onClick={() => handleCalendarClick('office365', calendarUrls.office365)}
                          className="flex items-center justify-center gap-2 bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors"
                        >
                          <Calendar className="h-4 w-4" />
                          Office 365
                        </button>
                      </div>

                      <button
                        onClick={downloadICSFile}
                        className="w-full flex items-center justify-center gap-2 bg-emerald-600 text-white py-3 px-4 rounded-lg hover:bg-emerald-700 transition-colors"
                      >
                        <Download className="h-4 w-4" />
                        Download Calendar File (.ics)
                      </button>

                      <div className="text-xs text-gray-400 text-center">
                        ⏰ Includes automatic reminders: 1 hour and 15 minutes before your booking
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 mt-6 pt-6 border-t border-gray-800">
                <button
                  onClick={() => setShowCalendarOptions(true)}
                  className="flex-1 bg-emerald-600 text-white py-3 px-4 rounded-lg hover:bg-emerald-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Calendar className="h-4 w-4" />
                  Add to Calendar
                </button>
                <button
                  onClick={onContinue || onClose}
                  className="flex-1 bg-gray-700 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  View My Bookings
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default BookingSuccessModal;
