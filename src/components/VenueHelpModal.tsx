
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/components/ui/use-toast';
import { HelpCircle, Send, Clock, CheckCircle, MessageSquare, Loader2, ArrowLeft } from 'lucide-react';
import {
  HELP_CATEGORIES,
  type HelpCategory,
  HelpRequest,
  ResolvedHelpRequest,
  GetUserHelpRequestsResult,
  GetUserResolvedHelpRequestsResult,
  HELP_STATUS
} from '@/types/help';

interface VenueHelpModalProps {
  venueId: string;
  venueName: string;
  children?: React.ReactNode;
}

interface Message {
  id: string;
  content: string;
  created_at: string;
  user_id: string;
  sender_id: string;
  is_read: boolean;
}

const CATEGORY_LABELS = {
  [HELP_CATEGORIES.BOOKING_ISSUES]: 'Booking Issues',
  [HELP_CATEGORIES.FACILITY_QUESTIONS]: 'Facility Questions',
  [HELP_CATEGORIES.PAYMENT_PROBLEMS]: 'Payment Problems',
  [HELP_CATEGORIES.GENERAL]: 'General Inquiry'
};

export const VenueHelpModal: React.FC<VenueHelpModalProps> = ({
  venueId,
  venueName,
  children
}) => {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [subject, setSubject] = useState('');
  const [category, setCategory] = useState<HelpCategory>(HELP_CATEGORIES.GENERAL);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'form' | 'chat' | 'past-requests' | 'view-past-request'>('form');
  const [helpRequest, setHelpRequest] = useState<HelpRequest | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [pastRequests, setPastRequests] = useState<ResolvedHelpRequest[]>([]);
  const [selectedPastRequest, setSelectedPastRequest] = useState<ResolvedHelpRequest | null>(null);
  const [pastRequestMessages, setPastRequestMessages] = useState<Message[]>([]);
  const [loadingPastRequests, setLoadingPastRequests] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check for existing help requests when modal opens
  useEffect(() => {
    const checkExistingRequests = async () => {
      if (!user || !open) return;

      try {
        setLoading(true);

        // Using PostgreSQL function to get user help requests
        const { data, error } = await supabase
          .rpc('get_user_help_requests', { p_user_id: user.id })
          .returns<GetUserHelpRequestsResult>();

        if (error) throw error;

        if (data && data.length > 0) {
          setHelpRequest(data[0]);
          setStep('chat');

          // Fetch messages for this help request
          await fetchMessages();
        } else {
          setStep('form');
        }
      } catch (error) {
        // Error checking help requests - details removed for production security
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      checkExistingRequests();
    }
  }, [user, open]);

  // Setup real-time listener for messages
  useEffect(() => {
    if (!user || !open) return;

    // Set up real-time subscription for messages
    const channel = supabase
      .channel('help_messages_changes')
      .on('postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          // Message received - payload details removed for production security
          // Check if this is a message without venue_id (help message)
          if (payload.new && payload.new.venue_id === null) {
            setMessages(prev => [...prev, payload.new as Message]);

            // Scroll to bottom after new message
            setTimeout(() => {
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, 100);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, open]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Fetch messages
  const fetchMessages = async () => {
    if (!user) return;

    try {
      // We're using the central messages table with venue_id as null for help requests
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('user_id', user.id)
        .is('venue_id', null) // Messages without venue_id are help requests
        .order('created_at', { ascending: true });

      if (error) throw error;
      // Help messages fetched - data details removed for production security
      setMessages(data || []);

      // Scroll to bottom after messages load
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } catch (error) {
      // Error fetching messages - details removed for production security
    }
  };

  // Helper function to check if a message is from admin
  const isAdminMessage = (message: Message) => {
    return message.sender_id !== message.user_id;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to submit a help request',
        variant: 'destructive'
      });
      return;
    }

    if (!subject.trim() || !message.trim()) {
      toast({
        title: 'Required Fields',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      // Create help request with venue context
      const { data: helpRequest, error: helpError } = await supabase
        .rpc('create_help_request', {
          p_user_id: user.id,
          p_subject: `${venueName}: ${subject}`,
          p_venue_id: venueId,
          p_category: category
        });

      if (helpError) throw helpError;

      // Send initial message
      const { error: messageError } = await supabase
        .from('messages')
        .insert({
          content: message,
          user_id: user.id,
          sender_id: user.id,
          venue_id: null, // Help messages have null venue_id
          is_read: false
        });

      if (messageError) throw messageError;

      toast({
        title: 'Help Request Submitted',
        description: `Your ticket #${helpRequest.ticket_number} has been created. We'll respond as soon as possible.`,
      });

      // Set the help request and switch to chat mode
      setHelpRequest(helpRequest);
      setStep('chat');

      // Reset form fields
      setSubject('');
      setMessage('');
      setCategory(HELP_CATEGORIES.GENERAL);

      // Fetch messages to show the conversation
      await fetchMessages();

    } catch (error) {
      // Error submitting help request - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to submit help request. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle sending new messages in chat mode
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !newMessage.trim()) return;

    setSending(true);
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          content: newMessage.trim(),
          user_id: user.id,
          sender_id: user.id,
          venue_id: null, // No venue_id means this is a help request message
        });

      if (error) throw error;

      // Update the last_message_at field in the help_request
      if (helpRequest) {
        await supabase
          .rpc('update_help_request_status', {
            p_help_request_id: helpRequest.id,
            p_status: HELP_STATUS.OPEN
          });
      }

      // Message sent successfully - details removed for production security
      setNewMessage('');

      // Refresh messages
      await fetchMessages();
    } catch (error) {
      // Error sending message - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    } finally {
      setSending(false);
    }
  };

  // Fetch past resolved requests
  const fetchPastRequests = async () => {
    if (!user) return;

    setLoadingPastRequests(true);
    try {
      const { data, error } = await supabase
        .rpc('get_user_resolved_help_requests', { p_user_id: user.id })
        .returns<GetUserResolvedHelpRequestsResult>();

      if (error) throw error;
      setPastRequests(data || []);
    } catch (error) {
      // Error fetching past requests - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to load past requests',
        variant: 'destructive',
      });
    } finally {
      setLoadingPastRequests(false);
    }
  };

  // Fetch messages for a past request
  const fetchPastRequestMessages = async (helpRequestId: string, createdAt: string) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('user_id', user.id)
        .is('venue_id', null)
        .gte('created_at', createdAt)
        .order('created_at', { ascending: true });

      if (error) throw error;
      setPastRequestMessages(data || []);
    } catch (error) {
      // Error fetching past request messages - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to load conversation history',
        variant: 'destructive',
      });
    }
  };

  // Handle viewing a past request
  const handleViewPastRequest = async (request: ResolvedHelpRequest) => {
    setSelectedPastRequest(request);
    setStep('view-past-request');
    await fetchPastRequestMessages(request.id, request.created_at);
  };

  // Format date for past requests
  const formatPastRequestDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" className="w-full">
            <HelpCircle className="w-4 h-4 mr-2" />
            Get Help
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="w-[95vw] max-w-[425px] h-[90vh] sm:h-[80vh] flex flex-col p-0 gap-0">
        <DialogHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
          <DialogTitle className="flex items-center justify-between text-sm sm:text-base">
            <div className="flex items-center gap-2">
              {(step === 'past-requests' || step === 'view-past-request') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (step === 'view-past-request') {
                      setStep('past-requests');
                      setSelectedPastRequest(null);
                      setPastRequestMessages([]);
                    } else {
                      setStep('form');
                      setPastRequests([]);
                    }
                  }}
                  className="p-1 h-auto"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              <HelpCircle className="w-5 h-5" />
              <span>
                {step === 'form' ? `Get Help - ${venueName}` :
                 step === 'past-requests' ? 'Past Requests' :
                 step === 'view-past-request' ? 'Request Details' :
                 'Support Chat'}
              </span>
            </div>
            {helpRequest && step === 'chat' && (
              <div className="flex items-center gap-2 text-sm">
                <span className="text-gray-600">#{helpRequest.ticket_number}</span>
                <Badge
                  className={
                    helpRequest.status === HELP_STATUS.RESOLVED ? 'bg-green-100 text-green-800' :
                    helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'bg-blue-100 text-blue-800' :
                    'bg-orange-100 text-orange-800'
                  }
                >
                  {helpRequest.status === HELP_STATUS.OPEN ? 'OPEN' :
                   helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'IN PROGRESS' :
                   'RESOLVED'}
                </Badge>
              </div>
            )}
            {selectedPastRequest && step === 'view-past-request' && (
              <div className="flex items-center gap-2 text-sm">
                <span className="text-gray-600">#{selectedPastRequest.ticket_number}</span>
                <Badge className="bg-green-100 text-green-800">
                  RESOLVED
                </Badge>
              </div>
            )}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center flex-grow">
            <Loader2 className="h-8 w-8 animate-spin text-indigo" />
          </div>
        ) : step === 'form' ? (
          <div className="px-4 py-3 sm:px-6 sm:py-4 space-y-4">
            {/* Past Requests Button */}
            <div className="pb-4 border-b">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setStep('past-requests');
                  fetchPastRequests();
                }}
                className="w-full flex items-center justify-center gap-2"
              >
                <Clock className="h-4 w-4" />
                View Past Requests
              </Button>
            </div>

            {/* New Request Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
            <div>
            <label className="text-sm font-medium mb-2 block">Category</label>
            <Select value={category} onValueChange={(value: HelpCategory) => setCategory(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Subject *</label>
            <Input
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Brief description of your inquiry"
              required
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Message *</label>
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Please provide details about your inquiry..."
              rows={3}
              className="min-h-[80px] sm:min-h-[100px]"
              required
            />
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-blue-800">
              <Clock className="w-4 h-4" />
              <span className="font-medium">Response Time:</span>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              We typically respond within 4-6 hours during business hours.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-2 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="w-full sm:flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="w-full sm:flex-1"
            >
              {loading ? (
                <div className="w-4 h-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Submit
                </>
              )}
            </Button>
          </div>
            </form>
          </div>
        ) : step === 'past-requests' ? (
          <div className="flex-grow overflow-y-auto px-3 py-2 sm:px-4 sm:py-4">
            {loadingPastRequests ? (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="h-8 w-8 animate-spin text-indigo" />
              </div>
            ) : pastRequests.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                <p>No past requests found</p>
                <p className="text-sm">All your resolved support tickets will appear here</p>
              </div>
            ) : (
              <div className="space-y-3">
                {pastRequests.map((request) => (
                  <div
                    key={request.id}
                    onClick={() => handleViewPastRequest(request)}
                    className="border rounded-lg p-3 sm:p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">#{request.ticket_number}</span>
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          RESOLVED
                        </Badge>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatPastRequestDate(request.created_at)}
                      </span>
                    </div>
                    <h3 className="font-medium text-gray-900 mb-1">{request.subject}</h3>
                    <p className="text-sm text-gray-600 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {request.first_message_preview}...
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : step === 'view-past-request' && selectedPastRequest ? (
          <div className="flex-grow overflow-y-auto px-3 py-2 sm:px-4 sm:py-4 space-y-3 sm:space-y-4">
            {/* Past Request Header */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 sm:p-4 rounded-lg mb-3 sm:mb-4 border border-green-200">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-semibold text-green-900">Ticket #{selectedPastRequest.ticket_number}</h3>
                <Badge className="bg-green-100 text-green-800">
                  RESOLVED
                </Badge>
              </div>
              <p className="text-sm text-gray-700 mb-2">
                <span className="font-medium">Subject:</span> {selectedPastRequest.subject}
              </p>
              <p className="text-xs text-gray-500">
                Created: {new Date(selectedPastRequest.created_at).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                  hour: 'numeric',
                  minute: 'numeric',
                  hour12: true
                })}
              </p>
            </div>

            {/* Past Request Messages */}
            {pastRequestMessages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                <p>No conversation history found</p>
              </div>
            ) : (
              pastRequestMessages.map((message) => (
                <div key={message.id} className="flex flex-col">
                  <div
                    className={`p-3 rounded-lg max-w-[85%] ${
                      isAdminMessage(message)
                        ? 'bg-indigo text-white self-start'
                        : 'bg-indigo-light/10 self-end'
                    }`}
                  >
                    <p className={isAdminMessage(message) ? 'text-white' : 'text-gray-800'}>
                      {message.content}
                    </p>
                    <span className={`text-xs mt-1 block ${
                      isAdminMessage(message) ? 'text-white/70' : 'text-gray-500'
                    }`}>
                      {isAdminMessage(message) ? 'Support' : 'You'} • {formatDate(message.created_at)}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        ) : (
          <>
            {/* Chat interface */}
            <div className="flex-grow overflow-y-auto px-3 py-2 sm:px-4 sm:py-4 space-y-3 sm:space-y-4">
              {helpRequest && (
                <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-3 sm:p-4 rounded-lg mb-3 sm:mb-4 border border-indigo-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-semibold text-indigo-900">Ticket #{helpRequest.ticket_number}</h3>
                    <Badge
                      className={
                        helpRequest.status === HELP_STATUS.RESOLVED ? 'bg-green-100 text-green-800' :
                        helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'bg-blue-100 text-blue-800' :
                        'bg-orange-100 text-orange-800'
                      }
                    >
                      {helpRequest.status === HELP_STATUS.OPEN ? 'OPEN' :
                       helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'IN PROGRESS' :
                       'RESOLVED'}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Subject:</span> {helpRequest.subject}
                  </p>
                  <p className="text-xs text-gray-500">
                    Created: {new Date(helpRequest.created_at).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric',
                      hour12: true
                    })}
                  </p>
                </div>
              )}

              {messages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                  <p>No messages yet. Start the conversation!</p>
                </div>
              ) : (
                messages.map((message) => (
                  <div key={message.id} className="flex flex-col">
                    <div
                      className={`p-3 rounded-lg max-w-[85%] ${
                        isAdminMessage(message)
                          ? 'bg-indigo text-white self-start'
                          : 'bg-indigo-light/10 self-end'
                      }`}
                    >
                      <p className={isAdminMessage(message) ? 'text-white' : 'text-gray-800'}>
                        {message.content}
                      </p>
                      <span className={`text-xs mt-1 block ${
                        isAdminMessage(message) ? 'text-white/70' : 'text-gray-500'
                      }`}>
                        {isAdminMessage(message) ? 'Support' : 'You'} • {formatDate(message.created_at)}
                      </span>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message input */}
            <form onSubmit={handleSendMessage} className="px-3 py-2 sm:px-4 sm:py-4 border-t mt-auto">
              <div className="flex gap-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-grow focus-visible:ring-indigo text-sm"
                  disabled={sending}
                />
                <Button
                  type="submit"
                  className="bg-indigo hover:bg-indigo-dark px-3 sm:px-4"
                  disabled={sending || !newMessage.trim()}
                >
                  {sending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
