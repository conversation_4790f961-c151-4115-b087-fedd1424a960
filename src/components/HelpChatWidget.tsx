
import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Send, X, Loader2, HelpCircle, MessageSquare, ArrowLeft, Clock, CheckCircle } from 'lucide-react';
import { 
  Dialog, 
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  HelpRequest,
  ResolvedHelpRequest,
  GetUserHelpRequestsResult,
  GetUserResolvedHelpRequestsResult,
  CreateHelpRequestResult,
  UpdateHelpRequestStatusResult,
  HELP_STATUS
} from '@/types/help';
import { Badge } from "@/components/ui/badge";

interface Message {
  id: string;
  content: string;
  created_at: string;
  user_id: string;
  sender_id: string;
  is_read: boolean;
}

const HelpChatWidget: React.FC = () => {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [subject, setSubject] = useState('');
  const [newMessage, setNewMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [helpRequest, setHelpRequest] = useState<HelpRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [step, setStep] = useState<'form' | 'chat' | 'past-requests' | 'view-past-request'>('form');
  const [pastRequests, setPastRequests] = useState<ResolvedHelpRequest[]>([]);
  const [selectedPastRequest, setSelectedPastRequest] = useState<ResolvedHelpRequest | null>(null);
  const [pastRequestMessages, setPastRequestMessages] = useState<Message[]>([]);
  const [loadingPastRequests, setLoadingPastRequests] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check for existing help requests
  useEffect(() => {
    const checkExistingRequests = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        
        // Using PostgreSQL function to get user help requests
        const { data, error } = await supabase
          .rpc('get_user_help_requests', { p_user_id: user.id })
          .returns<GetUserHelpRequestsResult>();
          
        if (error) throw error;
        
        if (data && data.length > 0) {
          setHelpRequest(data[0]);
          setStep('chat');
          
          // Fetch messages for this help request
          await fetchMessages();
        }
      } catch (error) {
        console.error('Error checking help requests:', error);
      } finally {
        setLoading(false);
      }
    };
    
    if (open) {
      checkExistingRequests();
    }
  }, [user, open]);

  // Fetch messages
  const fetchMessages = async () => {
    if (!user) return;
    
    try {
      // We're using the central messages table with venue_id as null for help requests
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('user_id', user.id)
        .is('venue_id', null) // Messages without venue_id are help requests
        .order('created_at', { ascending: true });

      if (error) throw error;
      console.log("Help messages fetched:", data);
      setMessages(data || []);
      
      // Scroll to bottom after messages load
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  // Setup real-time listener for messages
  useEffect(() => {
    if (!user || !open) return;
    
    // Set up real-time subscription for messages
    const channel = supabase
      .channel('help_messages_changes')
      .on('postgres_changes', 
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          console.log("Message received:", payload);
          // Check if this is a message without venue_id (help message)
          if (payload.new && payload.new.venue_id === null) {
            setMessages(prev => [...prev, payload.new as Message]);
            
            // Scroll to bottom after new message
            setTimeout(() => {
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, 100);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, open]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSubmitRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !subject.trim()) return;
    
    setSending(true);
    try {
      // Create a new help request using RPC call
      const { data: requestData, error: requestError } = await supabase
        .rpc('create_help_request', { 
          p_user_id: user.id, 
          p_subject: subject.trim() 
        })
        .returns<CreateHelpRequestResult>();
        
      if (requestError) throw requestError;
      
      if (requestData) {
        setHelpRequest(requestData);
      
        // Create initial message
        if (newMessage.trim()) {
          const { error: messageError } = await supabase
            .from('messages')
            .insert({
              content: newMessage.trim(),
              user_id: user.id,
              sender_id: user.id,
              venue_id: null, // No venue_id means this is a help request message
            });
            
          if (messageError) throw messageError;
        }
        
        setStep('chat');
        setNewMessage('');
        setSubject('');
        toast({
          title: 'Help Request Submitted',
          description: `Your ticket #${requestData.ticket_number} has been created. We will respond soon.`,
        });
        
        // Fetch messages to make sure we have the latest
        fetchMessages();
      }
    } catch (error) {
      console.error('Error submitting help request:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit help request',
        variant: 'destructive',
      });
    } finally {
      setSending(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !newMessage.trim()) return;
    
    setSending(true);
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          content: newMessage.trim(),
          user_id: user.id,
          sender_id: user.id,
          venue_id: null, // No venue_id means this is a help request message
        });
        
      if (error) throw error;
      
      // Update the last_message_at field in the help_request
      if (helpRequest) {
        await supabase
          .rpc('update_help_request_status', {
            p_help_request_id: helpRequest.id,
            p_status: HELP_STATUS.OPEN
          })
          .returns<UpdateHelpRequestStatusResult>();
      }
      
      // Message sent successfully - details removed for production security
      setNewMessage('');
    } catch (error) {
      // Error sending message - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    } finally {
      setSending(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });
  };

  // Fetch past resolved requests
  const fetchPastRequests = async () => {
    if (!user) return;

    try {
      setLoadingPastRequests(true);
      const { data, error } = await supabase
        .rpc('get_user_resolved_help_requests', { p_user_id: user.id })
        .returns<GetUserResolvedHelpRequestsResult>();

      if (error) throw error;
      setPastRequests(data || []);
    } catch (error) {
      // Error fetching past requests - details removed for production security
      toast({
        title: 'Error',
        description: 'Failed to load past requests',
        variant: 'destructive',
      });
    } finally {
      setLoadingPastRequests(false);
    }
  };

  // Fetch messages for a specific past request
  const fetchPastRequestMessages = async (requestId: string, requestCreatedAt: string) => {
    if (!user) return;

    try {
      // Fetch messages for this specific help request
      // We'll get messages that were created around the time of the request
      const requestDate = new Date(requestCreatedAt);
      const endDate = new Date(requestDate.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days after creation

      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('user_id', user.id)
        .is('venue_id', null)
        .gte('created_at', requestDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;
      setPastRequestMessages(data || []);
    } catch (error) {
      console.error('Error fetching past request messages:', error);
      toast({
        title: 'Error',
        description: 'Failed to load conversation history',
        variant: 'destructive',
      });
    }
  };

  // Handle viewing a past request
  const handleViewPastRequest = async (request: ResolvedHelpRequest) => {
    setSelectedPastRequest(request);
    setStep('view-past-request');
    await fetchPastRequestMessages(request.id, request.created_at);
  };

  // Format date for past requests
  const formatPastRequestDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Helper function to check if a message is from admin
  const isAdminMessage = (message: Message) => {
    return message.sender_id !== message.user_id;
  };

  if (!user) {
    return null; // Don't show the widget for non-authenticated users
  }

  return (
    <>
      {/* Fixed button at the bottom right */}
      <Button 
        onClick={() => setOpen(true)}
        className="fixed right-6 bottom-6 rounded-full w-12 h-12 p-0 bg-indigo hover:bg-indigo-dark shadow-lg z-50"
        aria-label="Help"
      >
        <HelpCircle className="h-6 w-6" />
      </Button>
      
      {/* Help Chat Dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="w-[95vw] max-w-[425px] h-[90vh] sm:h-[80vh] flex flex-col p-0 gap-0">
          <DialogHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b">
            <DialogTitle className="flex items-center justify-between text-sm sm:text-base">
              <div className="flex items-center gap-2">
                {(step === 'past-requests' || step === 'view-past-request') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      if (step === 'view-past-request') {
                        setStep('past-requests');
                        setSelectedPastRequest(null);
                        setPastRequestMessages([]);
                      } else {
                        setStep('form');
                        setPastRequests([]);
                      }
                    }}
                    className="p-1 h-auto"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                )}
                <span>
                  {step === 'form' ? 'Need Help?' :
                   step === 'past-requests' ? 'Past Requests' :
                   step === 'view-past-request' ? 'Request Details' :
                   'Support Chat'}
                </span>
              </div>
              {helpRequest && step === 'chat' && (
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-600">#{helpRequest.ticket_number}</span>
                  <Badge
                    variant={
                      helpRequest.status === HELP_STATUS.RESOLVED ? 'default' :
                      helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'secondary' :
                      'destructive'
                    }
                    className={
                      helpRequest.status === HELP_STATUS.RESOLVED ? 'bg-green-100 text-green-800 hover:bg-green-100' :
                      helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'bg-blue-100 text-blue-800 hover:bg-blue-100' :
                      'bg-orange-100 text-orange-800 hover:bg-orange-100'
                    }
                  >
                    {helpRequest.status === HELP_STATUS.OPEN ? 'OPEN' :
                     helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'IN PROGRESS' :
                     'RESOLVED'}
                  </Badge>
                </div>
              )}
              {selectedPastRequest && step === 'view-past-request' && (
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-600">#{selectedPastRequest.ticket_number}</span>
                  <Badge className="bg-green-100 text-green-800">
                    RESOLVED
                  </Badge>
                </div>
              )}
            </DialogTitle>
          </DialogHeader>
          
          {loading ? (
            <div className="flex justify-center items-center flex-grow">
              <Loader2 className="h-8 w-8 animate-spin text-indigo" />
            </div>
          ) : step === 'form' ? (
            <div className="px-4 py-3 sm:px-6 sm:py-4 space-y-4">
              {/* Past Requests Button */}
              <div className="pb-4 border-b">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setStep('past-requests');
                    fetchPastRequests();
                  }}
                  className="w-full flex items-center justify-center gap-2"
                >
                  <Clock className="h-4 w-4" />
                  View Past Requests
                </Button>
              </div>

              {/* New Request Form */}
              <form onSubmit={handleSubmitRequest} className="space-y-4">
                <div>
                  <Label htmlFor="subject">What do you need help with?</Label>
                  <Input
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="Booking issues, payment problems, etc."
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="message">Your message</Label>
                  <Textarea
                    id="message"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Please describe your issue in detail..."
                    rows={3}
                    className="min-h-[80px] sm:min-h-[120px]"
                    required
                  />
                </div>
                <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                  <Button type="button" variant="outline" onClick={() => setOpen(false)} className="w-full sm:w-auto">
                    Cancel
                  </Button>
                  <Button type="submit" disabled={sending || !subject.trim()} className="w-full sm:w-auto">
                    {sending ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                    Submit Request
                  </Button>
                </DialogFooter>
              </form>
            </div>
          ) : step === 'past-requests' ? (
            <div className="flex-grow overflow-y-auto px-3 py-2 sm:px-4 sm:py-4">
              {loadingPastRequests ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="h-8 w-8 animate-spin text-indigo" />
                </div>
              ) : pastRequests.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                  <p>No past requests found</p>
                  <p className="text-sm">All your resolved support tickets will appear here</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {pastRequests.map((request) => (
                    <div
                      key={request.id}
                      onClick={() => handleViewPastRequest(request)}
                      className="border rounded-lg p-3 sm:p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">#{request.ticket_number}</span>
                          <Badge className="bg-green-100 text-green-800 text-xs">
                            RESOLVED
                          </Badge>
                        </div>
                        <span className="text-xs text-gray-500">
                          {formatPastRequestDate(request.created_at)}
                        </span>
                      </div>
                      <h3 className="font-medium text-gray-900 mb-1">{request.subject}</h3>
                      <p className="text-sm text-gray-600 overflow-hidden" style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical'
                      }}>
                        {request.first_message_preview}...
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : step === 'view-past-request' && selectedPastRequest ? (
            <div className="flex-grow overflow-y-auto px-3 py-2 sm:px-4 sm:py-4 space-y-3 sm:space-y-4">
              {/* Past Request Header */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 sm:p-4 rounded-lg mb-3 sm:mb-4 border border-green-200">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-semibold text-green-900">Ticket #{selectedPastRequest.ticket_number}</h3>
                  <Badge className="bg-green-100 text-green-800">
                    RESOLVED
                  </Badge>
                </div>
                <p className="text-sm text-gray-700 mb-2">
                  <span className="font-medium">Subject:</span> {selectedPastRequest.subject}
                </p>
                <p className="text-xs text-gray-500">
                  Created: {new Date(selectedPastRequest.created_at).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                  })}
                </p>
              </div>

              {/* Past Request Messages */}
              {pastRequestMessages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                  <p>No conversation history found</p>
                </div>
              ) : (
                pastRequestMessages.map((message) => (
                  <div key={message.id} className="flex flex-col">
                    <div
                      className={`p-3 rounded-lg max-w-[85%] ${
                        isAdminMessage(message)
                          ? 'bg-indigo text-white self-start'
                          : 'bg-indigo-light/10 self-end'
                      }`}
                    >
                      <p className={isAdminMessage(message) ? 'text-white' : 'text-gray-800'}>
                        {message.content}
                      </p>
                      <span className={`text-xs mt-1 block ${
                        isAdminMessage(message) ? 'text-white/70' : 'text-gray-500'
                      }`}>
                        {isAdminMessage(message) ? 'Support' : 'You'} • {formatDate(message.created_at)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          ) : (
            <>
              {/* Active Chat interface */}
              <div className="flex-grow overflow-y-auto px-3 py-2 sm:px-4 sm:py-4 space-y-3 sm:space-y-4">
                {helpRequest && (
                  <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-3 sm:p-4 rounded-lg mb-3 sm:mb-4 border border-indigo-200">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold text-indigo-900">Ticket #{helpRequest.ticket_number}</h3>
                      <Badge
                        className={
                          helpRequest.status === HELP_STATUS.RESOLVED ? 'bg-green-100 text-green-800' :
                          helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'bg-blue-100 text-blue-800' :
                          'bg-orange-100 text-orange-800'
                        }
                      >
                        {helpRequest.status === HELP_STATUS.OPEN ? 'OPEN' :
                         helpRequest.status === HELP_STATUS.IN_PROGRESS ? 'IN PROGRESS' :
                         'RESOLVED'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">
                      <span className="font-medium">Subject:</span> {helpRequest.subject}
                    </p>
                    <p className="text-xs text-gray-500">
                      Created: {new Date(helpRequest.created_at).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric',
                        hour12: true
                      })}
                    </p>
                  </div>
                )}
                
                {messages.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                    <p>No messages yet. Start the conversation!</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div key={message.id} className="flex flex-col">
                      <div 
                        className={`p-3 rounded-lg max-w-[85%] ${
                          isAdminMessage(message) 
                            ? 'bg-indigo text-white self-start' 
                            : 'bg-indigo-light/10 self-end'
                        }`}
                      >
                        <p className={isAdminMessage(message) ? 'text-white' : 'text-gray-800'}>
                          {message.content}
                        </p>
                        <span className={`text-xs mt-1 block ${
                          isAdminMessage(message) ? 'text-white/70' : 'text-gray-500'
                        }`}>
                          {isAdminMessage(message) ? 'Support' : 'You'} • {formatDate(message.created_at)}
                        </span>
                      </div>
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>
              
              {/* Message input */}
              <form onSubmit={handleSendMessage} className="px-3 py-2 sm:px-4 sm:py-4 border-t mt-auto">
                <div className="flex gap-2">
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-grow focus-visible:ring-indigo text-sm"
                    disabled={sending}
                  />
                  <Button
                    type="submit"
                    className="bg-indigo hover:bg-indigo-dark px-3 sm:px-4"
                    disabled={sending || !newMessage.trim()}
                  >
                    {sending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </form>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HelpChatWidget;
