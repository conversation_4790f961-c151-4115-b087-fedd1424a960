import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";

// Get environment variables
const RAZORPAY_KEY_ID = Deno.env.get("RAZORPAY_KEY_ID") || "";
const RAZORPAY_KEY_SECRET = Deno.env.get("RAZORPAY_KEY_SECRET") || "";

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS"
};

// Function to verify payment status with Razorpay
async function verifyPaymentStatus(paymentId: string, orderId: string) {
  try {
    // Verifying payment status - payment details removed for production security

    // Create basic auth header for Razorpay API
    const auth = btoa(`${RAZORPAY_KEY_ID}:${RAZORPAY_KEY_SECRET}`);
    
    // Fetch payment details from Razorpay
    const response = await fetch(`https://api.razorpay.com/v1/payments/${paymentId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // Razorpay API error - details removed for production security
      return {
        success: false,
        error: `Razorpay API error: ${response.status}`
      };
    }

    const paymentData = await response.json();
    // Payment data from Razorpay - details removed for production security

    // Verify order ID matches
    if (paymentData.order_id !== orderId) {
      // Order ID mismatch - details removed for production security
      return {
        success: false,
        error: 'Order ID mismatch'
      };
    }

    // Check payment status
    const isSuccessful = paymentData.status === 'captured' || paymentData.status === 'authorized';

    // Payment verification result - details removed for production security

    return {
      success: true,
      status: paymentData.status,
      isSuccessful,
      amount: paymentData.amount,
      method: paymentData.method,
      order_id: paymentData.order_id
    };

  } catch (error) {
    // Exception in payment verification - details removed for production security
    return {
      success: false,
      error: error.message
    };
  }
}

// Main handler
export default serve(async (req) => {
  // Payment verification request received - method details removed for production security

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders
    });
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({
      success: false,
      error: "Method not allowed"
    }), {
      status: 405,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }

  try {
    // Parse request body
    const body = await req.json();
    const { paymentId, orderId } = body;

    if (!paymentId || !orderId) {
      return new Response(JSON.stringify({
        success: false,
        error: "Missing paymentId or orderId"
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }

    // Verify payment status
    const result = await verifyPaymentStatus(paymentId, orderId);

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });

  } catch (error) {
    // Error processing payment verification - details removed for production security
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
});
