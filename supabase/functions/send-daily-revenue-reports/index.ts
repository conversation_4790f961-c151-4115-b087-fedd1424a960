import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    if (!authKey) {
      console.error('MSG91 configuration missing')
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body to get date (optional, defaults to current date)
    let reportDate = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
    
    try {
      const body = await req.json()
      if (body.date) {
        reportDate = body.date
      }
    } catch {
      // Use default date if no body or invalid JSON
    }

    console.log(`📊 Starting daily revenue reports for date: ${reportDate}`)

    // First, validate and fix any data inconsistencies
    console.log(`🔍 Validating daily earnings data for date: ${reportDate}`)

    // Check for venues with bookings but missing/incorrect daily earnings
    const { data: validationCheck } = await supabaseAdmin
      .rpc('validate_daily_earnings_before_reports', { p_date: reportDate })

    if (validationCheck) {
      console.log(`✅ Data validation completed: ${JSON.stringify(validationCheck)}`)
    }

    // Get venue daily earnings data with online/offline separation
    const { data: venueReports, error: venueError } = await supabaseAdmin
      .rpc('get_venue_daily_earnings_detailed', { p_date: reportDate })

    if (venueError) {
      console.error('Error fetching venue reports:', venueError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch venue reports', details: venueError }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!venueReports || venueReports.length === 0) {
      console.log('No venue reports found for date:', reportDate)
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No venue reports found for the specified date',
          date: reportDate,
          reports_sent: 0
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`📤 Found ${venueReports.length} venue reports to send`)

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    // Process each venue report
    for (const report of venueReports) {
      try {
        // Validate phone number format
        const adminPhone = report.admin_phone?.toString().trim()
        if (!adminPhone || adminPhone === '000000000' || adminPhone.length < 10) {
          console.warn(`Skipping ${report.venue_name}: Invalid phone number ${adminPhone}`)
          errorCount++
          errors.push(`${report.venue_name}: Invalid phone number`)
          continue
        }

        // Ensure phone number has +91 prefix
        const formattedPhone = adminPhone.startsWith('+91') ? adminPhone : `+91${adminPhone}`

        // FIXED: Use dynamic platform fee from database (Grid२Play transparency USP)
        const platformFeePercentage = parseFloat(report.platform_fee_percentage || '5.0') // Dynamic from venue database
        const onlineUserPaid = parseFloat(report.online_gross_revenue || '0') // What user actually paid (after discount)
        const offlineGrossRevenue = parseFloat(report.offline_gross_revenue || '0')
        const onlineOriginalAmount = parseFloat(report.online_original_revenue || '0') // Original amount before discount
        const platformFeeAmount = onlineOriginalAmount * (platformFeePercentage / 100) // Correct platform fee calculation
        const totalDiscountGiven = parseFloat(report.total_discount_given || '0')

        // FIXED: Use correct settlement amount directly from backend (includes TDS deduction)
        const onlineNetSettlement = parseFloat(report.online_net_revenue || '0') // Correct settlement from backend

        // Prepare MSG91 WhatsApp payload for new dailyrevenue_reports template (15 variables)
        const msg91Payload = {
          integrated_number: integratedNumber,
          content_type: "template",
          payload: {
            messaging_product: "whatsapp",
            type: "template",
            template: {
              name: "dailyrevenue_reports",
              language: {
                code: "en_US",
                policy: "deterministic"
              },
              namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
              to_and_components: [
                {
                  to: [formattedPhone],
                  components: {
                    body_1: { type: "text", value: report.report_date }, // {{1}} - Date
                    body_2: { type: "text", value: report.venue_name }, // {{2}} - Venue Name
                    body_3: { type: "text", value: report.total_bookings }, // {{3}} - Total Bookings
                    body_4: { type: "text", value: report.confirmed_bookings }, // {{4}} - Confirmed Bookings
                    body_5: { type: "text", value: report.cancelled_bookings }, // {{5}} - Cancelled Bookings
                    body_6: { type: "text", value: report.online_bookings || '0' }, // {{6}} - Online Bookings
                    body_7: { type: "text", value: onlineOriginalAmount.toFixed(0) }, // {{7}} - Online Gross Revenue (original amount)
                    body_8: { type: "text", value: platformFeeAmount.toFixed(0) }, // {{8}} - Platform Fee Amount (correct calculation)
                    body_9: { type: "text", value: platformFeePercentage.toString() }, // {{9}} - Platform Fee Percentage (from database)
                    body_10: { type: "text", value: onlineNetSettlement.toFixed(0) }, // {{10}} - Online Net Settlement
                    body_11: { type: "text", value: report.coupon_usage_count || '0' }, // {{11}} - Coupon Bookings
                    body_12: { type: "text", value: totalDiscountGiven.toFixed(0) }, // {{12}} - Total Discount
                    body_13: { type: "text", value: onlineNetSettlement.toFixed(0) }, // {{13}} - Net Settlement (same as 10)
                    body_14: { type: "text", value: report.offline_bookings || '0' }, // {{14}} - Cash Bookings
                    body_15: { type: "text", value: offlineGrossRevenue.toFixed(0) } // {{15}} - Cash Revenue
                  }
                }
              ]
            }
          }
        }

        console.log(`📤 Sending daily report to ${report.admin_name} (${formattedPhone}) for venue: ${report.venue_name}`)

        // Send WhatsApp message via MSG91 API
        const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'authkey': authKey
          },
          body: JSON.stringify(msg91Payload)
        })

        if (response.ok) {
          successCount++
          console.log(`✅ Daily report sent successfully to ${report.venue_name}`)
        } else {
          const errorText = await response.text()
          console.error(`❌ Failed to send daily report to ${report.venue_name}:`, errorText)
          errorCount++
          errors.push(`Failed to send to ${report.venue_name}: ${errorText}`)
        }

        // Add delay between messages to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error(`❌ Error processing report for ${report.venue_name}:`, error)
        errorCount++
        errors.push(`Error processing ${report.venue_name}: ${error.message}`)
      }
    }

    // Log execution results
    const executionResult = {
      success: successCount > 0,
      message: `Daily revenue reports completed. Sent: ${successCount}, Failed: ${errorCount}`,
      date: reportDate,
      total_reports: venueReports.length,
      successful_sends: successCount,
      failed_sends: errorCount,
      errors: errors.length > 0 ? errors : undefined
    }

    console.log(`📊 Daily revenue reports execution completed:`, executionResult)

    return new Response(
      JSON.stringify(executionResult),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Critical error in daily revenue reports:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Critical error in daily revenue reports', 
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
