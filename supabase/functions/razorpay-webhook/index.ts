
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// Get environment variables
const RAZORPAY_KEY_ID = Deno.env.get("RAZORPAY_KEY_ID") || "";
const RAZORPAY_WEBHOOK_SECRET = Deno.env.get("RAZORPAY_WEBHOOK_SECRET") || "";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type, x-razorpay-signature"
};

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Verify Razorpay signature using Web Crypto API (Deno compatible)
async function verifySignature(body: string, signature: string): Promise<boolean> {
  if (!RAZORPAY_WEBHOOK_SECRET) {
    console.error("RAZORPAY_WEBHOOK_SECRET is not configured");
    return false;
  }

  try {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      "raw",
      encoder.encode(RAZORPAY_WEBHOOK_SECRET),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const signatureBuffer = await crypto.subtle.sign(
      "HMAC",
      key,
      encoder.encode(body)
    );

    const expectedSignature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    return expectedSignature === signature;
  } catch (error) {
    console.error("Error verifying signature:", error);
    return false;
  }
}

// Log payment event to payment_logs table
async function logPaymentEvent(event: any, status: string = 'received') {
  try {
    const payment = event.payload?.payment?.entity || event.payload?.order?.entity;
    const order = event.payload?.order?.entity;
    
    const logData = {
      event_type: event.event,
      status: status,
      payload: event,
      razorpay_payment_id: payment?.id || null,
      razorpay_order_id: payment?.order_id || order?.id || null,
      payment_id: payment?.id || order?.id || null,
      amount: payment?.amount ? payment.amount / 100 : (order?.amount ? order.amount / 100 : null),
      booking_id: null // Will be filled if we can match with a booking
    };

    // Inserting into payment_logs - data details removed for production security

    const { data, error } = await supabase
      .from('payment_logs')
      .insert(logData)
      .select()
      .single();

    if (error) {
      // Error logging payment event - details removed for production security
      return null;
    }

    // Payment event logged successfully - ID removed for production security
    return data;
  } catch (error) {
    // Error in logPaymentEvent - details removed for production security
    return null;
  }
}

// Process payment event
async function processPayment(event: any) {
  // Processing payment event - event details removed for production security

  // Log the event first
  await logPaymentEvent(event, 'processing');

  try {
    const payment = event.payload.payment.entity;
    // Payment details logging removed for production security

    // Extract booking information from notes
    const notes = payment.notes || {};
    const courtId = notes.court_id;
    const date = notes.date;
    const slots = notes.slots ? notes.slots.split(', ') : [];
    const venueId = notes.venueId;
    const sportId = notes.sportId;

    if (!courtId || !date || !slots.length) {
      // Missing booking information in payment notes - details removed for production security
      await logPaymentEvent(event, 'failed_missing_booking_info');
      return {
        success: false,
        error: "Missing booking information"
      };
    }

    // Find or create the order in our database
    let orderData;
    const { data: existingOrder, error: orderError } = await supabase
      .from('payment_orders')
      .select('*')
      .eq('razorpay_order_id', payment.order_id)
      .single();

    if (orderError && orderError.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error("Error finding order:", orderError);
    }

    if (!existingOrder) {
      // Create a new order record
      console.log("Creating new payment order record");
      const { data: newOrder, error: createError } = await supabase
        .from('payment_orders')
        .insert({
          razorpay_order_id: payment.order_id,
          razorpay_payment_id: payment.id,
          amount: payment.amount / 100,
          status: payment.status,
          payment_method: payment.method,
          metadata: {
            court_id: courtId,
            date: date,
            slots: slots,
            venue_id: venueId,
            sport_id: sportId
          }
        })
        .select()
        .single();

      if (createError) {
        console.error("Error creating order record:", createError);
        await logPaymentEvent(event, 'failed_order_creation');
        return {
          success: false,
          error: "Failed to create order record"
        };
      }
      orderData = newOrder;
      console.log("Created new order record:", newOrder.id);
    } else {
      // Update the existing order
      console.log("Updating existing order record");
      const { data: updatedOrder, error: updateError } = await supabase
        .from('payment_orders')
        .update({
          razorpay_payment_id: payment.id,
          status: payment.status,
          payment_method: payment.method,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingOrder.id)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating order:", updateError);
        await logPaymentEvent(event, 'failed_order_update');
        return {
          success: false,
          error: "Failed to update order"
        };
      }
      orderData = updatedOrder;
    }

    // If payment is captured, create the booking
    if (payment.status === 'captured') {
      console.log("Payment captured, creating booking");
      
      // Parse time slots
      const timeSlots = slots.map((slot: string) => {
        const [start, end] = slot.split('-');
        return {
          start_time: start.trim(),
          end_time: end.trim()
        };
      });

      if (!timeSlots.length) {
        console.error("No valid time slots found");
        await logPaymentEvent(event, 'failed_no_slots');
        return {
          success: false,
          error: "No valid time slots"
        };
      }

      // Create booking for each time slot
      for (const slot of timeSlots) {
        try {
          const { data: booking, error: bookingError } = await supabase
            .rpc('create_booking_with_lock', {
              p_court_id: courtId,
              p_user_id: payment.notes.user_id || null,
              p_booking_date: date,
              p_start_time: slot.start_time,
              p_end_time: slot.end_time,
              p_total_price: payment.amount / 100 / timeSlots.length,
              p_guest_name: payment.notes.guest_name || payment.notes.name || null,
              p_guest_phone: payment.notes.guest_phone || payment.contact || null,
              p_payment_reference: payment.id,
              p_payment_status: 'completed'
            });

          if (bookingError) {
            console.error("Error creating booking:", bookingError);
            await logPaymentEvent(event, 'failed_booking_creation');
            return {
              success: false,
              error: "Failed to create booking: " + bookingError.message
            };
          }
          console.log("Created booking:", booking);
        } catch (error) {
          console.error("Exception during booking creation:", error);
          await logPaymentEvent(event, 'failed_booking_exception');
          return {
            success: false,
            error: "Exception during booking creation: " + error.message
          };
        }
      }

      await logPaymentEvent(event, 'completed');
      return {
        success: true,
        message: "Payment processed and booking created"
      };
    }

    await logPaymentEvent(event, 'processed');
    return {
      success: true,
      message: "Payment event processed"
    };
  } catch (error) {
    console.error("Error processing payment:", error);
    await logPaymentEvent(event, 'failed_processing');
    return {
      success: false,
      error: error.message
    };
  }
}

// Main handler
export default serve(async (req) => {
  console.log("Razorpay webhook received. Method:", req.method, "URL:", req.url);

  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders
    });
  }

  try {
    // Get the signature from headers
    const signature = req.headers.get("x-razorpay-signature");
    if (!signature) {
      console.error("Missing Razorpay signature");
      return new Response(JSON.stringify({
        success: false,
        error: "Missing signature"
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }

    // Get the raw body
    const body = await req.text();
    console.log("Webhook body received, length:", body.length);

    // Verify signature
    const isValid = await verifySignature(body, signature);
    if (!isValid) {
      console.error("Invalid signature");
      return new Response(JSON.stringify({
        success: false,
        error: "Invalid signature"
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }

    console.log("Signature verified successfully");

    // Parse the event
    const event = JSON.parse(body);
    console.log("Event type:", event.event);
    console.log("Event payload keys:", Object.keys(event.payload || {}));

    // Process based on event type
    let result;
    switch (event.event) {
      case "payment.authorized":
      case "payment.captured":
      case "payment.failed":
        result = await processPayment(event);
        break;
      default:
        console.log("Unhandled event type:", event.event);
        await logPaymentEvent(event, 'unhandled');
        result = {
          success: true,
          message: "Event acknowledged but not processed"
        };
    }

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    console.error("Error processing webhook:", error);
    
    // Try to log this error event too
    try {
      await logPaymentEvent({
        event: 'webhook_error',
        payload: { error: error.message }
      }, 'webhook_error');
    } catch (logError) {
      console.error("Failed to log webhook error:", logError);
    }

    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
});
