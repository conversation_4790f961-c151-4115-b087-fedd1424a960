
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import "https://deno.land/x/xhr@0.1.0/mod.ts";

// Get Razorpay API keys from environment variables
const RAZORPAY_KEY_ID = Deno.env.get("RAZORPAY_KEY_ID") || "";
const RAZORPAY_KEY_SECRET = Deno.env.get("RAZORPAY_KEY_SECRET") || "";

// Security configuration
const BOOKING_SECURITY_SECRET = Deno.env.get("BOOKING_SECURITY_SECRET") || "";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface CreateOrderRequest {
  amount: number;
  currency?: string;
  receipt?: string;
  notes?: Record<string, string>;
  // Enhanced security fields
  courtId?: string;
  date?: string;
  startTime?: string;
  endTime?: string;
  couponCode?: string;
  guestName?: string;
  guestPhone?: string;
  csrfToken?: string;
}

// Security utility functions
function sanitizeInput(input: string): string {
  return input.replace(/[<>"'&]/g, '').trim().substring(0, 100);
}

function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone);
}

function validateAmount(amount: number): { valid: boolean; error?: string } {
  if (!amount || amount <= 0) {
    return { valid: false, error: "Amount must be greater than 0" };
  }
  if (amount < 100) { // Minimum ₹1 (100 paise)
    return { valid: false, error: "Amount must be at least ₹1" };
  }
  // No maximum limit - users can book any amount they need
  return { valid: true };
}

// Security logging function
async function logSecurityEvent(
  supabase: any,
  eventType: string,
  severity: string,
  details: any,
  userId?: string
): Promise<void> {
  try {
    await supabase.from('security_logs').insert({
      event_type: eventType,
      severity: severity,
      details: details,
      user_id: userId,
      created_at: new Date().toISOString()
    });
  } catch (error) {
    // Failed to log security event - details removed for production security
  }
}

export default serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
    });
  }

  const startTime = Date.now();
  let userId: string | null = null;
  let supabase: any = null;

  try {
    // Initialize Supabase client for security logging
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

    if (supabaseUrl && supabaseServiceKey) {
      supabase = createClient(supabaseUrl, supabaseServiceKey);
    }

    // Get user from JWT token
    const authHeader = req.headers.get('authorization');
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '');
      const { data: { user }, error } = await supabase.auth.getUser(token);
      if (user) userId = user.id;
    }

    // Security check: Require authentication for payment orders
    if (!userId) {
      await logSecurityEvent(supabase, 'PAYMENT_ORDER_UNAUTHORIZED_ACCESS', 'HIGH', {
        ip_address: req.headers.get('x-forwarded-for') || 'unknown',
        user_agent: req.headers.get('user-agent') || 'unknown'
      });
      throw new Error("Authentication required for payment orders");
    }

    // Check if API keys are configured
    if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
      await logSecurityEvent(supabase, 'PAYMENT_API_KEYS_MISSING', 'CRITICAL', {
        user_id: userId
      }, userId);
      throw new Error("Payment service temporarily unavailable");
    }

    // Parse request body with enhanced validation
    const requestBody = await req.json() as CreateOrderRequest;
    const {
      amount,
      currency = "INR",
      receipt = "",
      notes = {},
      courtId,
      date,
      startTime: bookingStartTime,
      endTime: bookingEndTime,
      couponCode,
      guestName,
      guestPhone,
      csrfToken
    } = requestBody;

    // Enhanced amount validation (no restrictive limits)
    const amountValidation = validateAmount(amount);
    if (!amountValidation.valid) {
      if (supabase) {
        await logSecurityEvent(supabase, 'PAYMENT_INVALID_AMOUNT', 'MEDIUM', {
          submitted_amount: amount,
          error: amountValidation.error,
          user_id: userId
        }, userId);
      }
      throw new Error(amountValidation.error);
    }

    // Sanitize inputs if provided (security without restrictions)
    const sanitizedData = {
      guestName: guestName ? sanitizeInput(guestName) : undefined,
      guestPhone: guestPhone ? sanitizeInput(guestPhone) : undefined,
      couponCode: couponCode ? sanitizeInput(couponCode.toUpperCase()) : undefined,
      courtId: courtId ? sanitizeInput(courtId) : undefined,
      date: date ? sanitizeInput(date) : undefined
    };

    // Validate phone number if provided (but don't restrict booking)
    if (sanitizedData.guestPhone && !validatePhoneNumber(sanitizedData.guestPhone)) {
      if (supabase) {
        await logSecurityEvent(supabase, 'PAYMENT_INVALID_PHONE', 'LOW', {
          phone: sanitizedData.guestPhone,
          user_id: userId
        }, userId);
      }
      // Don't throw error - just log for monitoring
      // Invalid phone number format - details removed for production security
    }

    // Log payment order creation attempt (for monitoring, not restriction)
    if (supabase) {
      await logSecurityEvent(supabase, 'PAYMENT_ORDER_CREATION_ATTEMPT', 'LOW', {
        amount: amount,
        court_id: sanitizedData.courtId,
        user_id: userId,
        ip_address: req.headers.get('x-forwarded-for') || 'unknown',
        has_coupon: !!sanitizedData.couponCode
      }, userId);
    }

    // The total amount should be in paise
    const amountInPaise = Math.round(amount);
    // Creating Razorpay order - amount details removed for production security
    
    // Create Basic Authorization header
    const auth = btoa(`${RAZORPAY_KEY_ID}:${RAZORPAY_KEY_SECRET}`);
    
    // Create simple Razorpay order - all payments go directly to main account
    const orderPayload = {
      amount: amountInPaise, 
      currency: currency,
      receipt: receipt,
      notes: notes
    };
    
    // Razorpay order payload logging removed for production security
    
    // Create Razorpay order
    const razorpayResponse = await fetch("https://api.razorpay.com/v1/orders", {
      method: "POST",
      headers: {
        "Authorization": `Basic ${auth}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(orderPayload)
    });

    const razorpayData = await razorpayResponse.json();

    if (!razorpayResponse.ok) {
      // Razorpay API error - details removed for production security
      throw new Error(razorpayData.error?.description || "Failed to create Razorpay order");
    }

    // Razorpay order created successfully - order ID removed for production security

    // Log successful payment order creation
    if (supabase) {
      await logSecurityEvent(supabase, 'PAYMENT_ORDER_CREATED_SUCCESS', 'LOW', {
        order_id: razorpayData.id,
        amount: amountInPaise,
        court_id: sanitizedData.courtId,
        user_id: userId,
        processing_time_ms: Date.now() - startTime
      }, userId);
    }

    // Return successful response with Razorpay order details
    return new Response(
      JSON.stringify({
        order: razorpayData,
        key_id: RAZORPAY_KEY_ID
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200
      }
    );
  } catch (error) {
    // Error in create-razorpay-order function - details removed for production security

    // Log payment order creation failure
    if (supabase && userId) {
      await logSecurityEvent(supabase, 'PAYMENT_ORDER_CREATION_FAILED', 'HIGH', {
        error: error.message || 'Unknown error',
        user_id: userId,
        processing_time_ms: Date.now() - startTime
      }, userId);
    }

    // Return error response
    return new Response(
      JSON.stringify({
        error: error.message || "An unexpected error occurred"
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    );
  }
});
