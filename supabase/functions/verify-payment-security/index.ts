import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PaymentVerificationRequest {
  paymentId: string;
  orderId: string;
  signature: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { paymentId, orderId, signature }: PaymentVerificationRequest = await req.json()

    // Validate input
    if (!paymentId || !orderId || !signature) {
      return new Response(
        JSON.stringify({ error: 'Missing payment verification data' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Step 1: Verify Razorpay signature
    const razorpayKeySecret = Deno.env.get('RAZORPAY_KEY_SECRET')
    if (!razorpayKeySecret) {
      console.error('Razorpay secret not configured')
      return new Response(
        JSON.stringify({ error: 'Payment verification service error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create expected signature
    const expectedSignature = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(razorpayKeySecret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    ).then(key => 
      crypto.subtle.sign(
        'HMAC',
        key,
        new TextEncoder().encode(`${orderId}|${paymentId}`)
      )
    ).then(signature => 
      Array.from(new Uint8Array(signature))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
    )

    if (expectedSignature !== signature) {
      // Log signature verification failure
      await supabaseClient.from('security_logs').insert({
        event_type: 'PAYMENT_SIGNATURE_VERIFICATION_FAILED',
        severity: 'CRITICAL',
        details: {
          user_id: user.id,
          payment_id: paymentId,
          order_id: orderId,
          provided_signature: signature,
          expected_signature: expectedSignature
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ 
          valid: false, 
          error: 'Payment signature verification failed' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Step 2: Verify payment intent exists and is valid
    const { data: paymentIntent, error: intentError } = await supabaseClient
      .from('payment_intents')
      .select('*')
      .eq('order_id', orderId)
      .eq('user_id', user.id)
      .eq('status', 'created')
      .gt('expires_at', new Date().toISOString())
      .single()

    if (intentError || !paymentIntent) {
      // Log invalid payment intent
      await supabaseClient.from('security_logs').insert({
        event_type: 'INVALID_PAYMENT_INTENT',
        severity: 'HIGH',
        details: {
          user_id: user.id,
          payment_id: paymentId,
          order_id: orderId,
          error: intentError?.message || 'Payment intent not found or expired'
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ 
          valid: false, 
          error: 'Invalid or expired payment intent' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Step 3: Verify payment with Razorpay API
    const razorpayKeyId = Deno.env.get('RAZORPAY_KEY_ID')
    const paymentVerificationResponse = await fetch(
      `https://api.razorpay.com/v1/payments/${paymentId}`,
      {
        headers: {
          'Authorization': `Basic ${btoa(`${razorpayKeyId}:${razorpayKeySecret}`)}`
        }
      }
    )

    if (!paymentVerificationResponse.ok) {
      const errorText = await paymentVerificationResponse.text()
      // Razorpay payment verification failed - error details removed for production security
      
      await supabaseClient.from('security_logs').insert({
        event_type: 'RAZORPAY_PAYMENT_VERIFICATION_FAILED',
        severity: 'HIGH',
        details: {
          user_id: user.id,
          payment_id: paymentId,
          error: errorText
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ 
          valid: false, 
          error: 'Payment verification with Razorpay failed' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const paymentData = await paymentVerificationResponse.json()

    // Verify payment status and amount
    if (paymentData.status !== 'captured' && paymentData.status !== 'authorized') {
      await supabaseClient.from('security_logs').insert({
        event_type: 'PAYMENT_STATUS_INVALID',
        severity: 'HIGH',
        details: {
          user_id: user.id,
          payment_id: paymentId,
          status: paymentData.status,
          expected_status: 'captured or authorized'
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ 
          valid: false, 
          error: 'Payment not completed successfully' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify payment amount matches intent
    const expectedAmount = Math.round(paymentIntent.final_price * 100)
    if (paymentData.amount !== expectedAmount) {
      await supabaseClient.from('security_logs').insert({
        event_type: 'PAYMENT_AMOUNT_MISMATCH',
        severity: 'CRITICAL',
        details: {
          user_id: user.id,
          payment_id: paymentId,
          expected_amount: expectedAmount,
          actual_amount: paymentData.amount
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ 
          valid: false, 
          error: 'Payment amount verification failed' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Step 4: Update payment intent status
    const { error: updateError } = await supabaseClient
      .from('payment_intents')
      .update({ 
        status: 'verified',
        payment_id: paymentId,
        verified_at: new Date().toISOString()
      })
      .eq('id', paymentIntent.id)

    if (updateError) {
      console.error('Payment intent update failed:', updateError)
      return new Response(
        JSON.stringify({ 
          valid: false, 
          error: 'Payment verification update failed' 
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Log successful payment verification
    await supabaseClient.from('security_logs').insert({
      event_type: 'PAYMENT_VERIFICATION_SUCCESS',
      severity: 'LOW',
      details: {
        user_id: user.id,
        payment_id: paymentId,
        order_id: orderId,
        amount: paymentData.amount
      },
      user_id: user.id
    })

    return new Response(
      JSON.stringify({ 
        valid: true,
        payment_intent: paymentIntent,
        payment_data: {
          id: paymentData.id,
          amount: paymentData.amount,
          status: paymentData.status,
          method: paymentData.method
        }
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Payment verification error:', error)
    return new Response(
      JSON.stringify({ 
        valid: false, 
        error: 'Payment verification service error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
